#!/usr/bin/env ruby

# Test script to verify Firebase configuration
require_relative 'config/environment'

puts "Testing Firebase Configuration..."
puts "=" * 50

# Test 1: Check if Firebase config file is accessible
puts "\n1. Testing Firebase config file access..."
begin
  config_path = Rails.root.join("config", "travelgator-firebase-adminsdk-fbsvc-7952955d76.json")
  
  if File.exist?(config_path)
    puts "   ✓ Firebase config file exists at: #{config_path}"
    
    config = JSON.parse(File.read(config_path))
    puts "   ✓ Firebase config file is valid JSON"
    puts "   Project ID: #{config['project_id']}"
    puts "   Client Email: #{config['client_email']}"
    puts "   Private Key ID: #{config['private_key_id']}"
    
    # Verify required fields
    required_fields = ['type', 'project_id', 'private_key', 'client_email']
    missing_fields = required_fields.select { |field| config[field].nil? || config[field].empty? }
    
    if missing_fields.empty?
      puts "   ✓ All required Firebase fields are present"
    else
      puts "   ✗ Missing required fields: #{missing_fields.join(', ')}"
    end
  else
    puts "   ✗ Firebase config file not found"
  end
rescue StandardError => e
  puts "   ✗ Error reading Firebase config: #{e.message}"
end

# Test 2: Test FirebaseAuthService initialization
puts "\n2. Testing FirebaseAuthService configuration loading..."
begin
  # This will test the load_firebase_config method
  firebase_service = FirebaseAuthService
  
  # Use reflection to test the private method
  config = firebase_service.send(:load_firebase_config)
  
  puts "   ✓ FirebaseAuthService can load configuration"
  puts "   Loaded project_id: #{config['project_id']}"
  
rescue StandardError => e
  puts "   ✗ FirebaseAuthService config loading failed: #{e.message}"
  puts "   Backtrace: #{e.backtrace.first(3).join("\n")}"
end

# Test 3: Test Firebase public key retrieval
puts "\n3. Testing Firebase public key retrieval..."
begin
  require 'net/http'
  require 'uri'
  
  jwks_uri = "https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>"
  response = Net::HTTP.get(URI(jwks_uri))
  certs = JSON.parse(response)
  
  if certs.is_a?(Hash) && !certs.empty?
    puts "   ✓ Firebase public keys retrieved successfully"
    puts "   Available key IDs: #{certs.keys.join(', ')}"
  else
    puts "   ✗ Invalid response from Firebase public key endpoint"
  end
rescue StandardError => e
  puts "   ✗ Failed to retrieve Firebase public keys: #{e.message}"
end

# Test 4: Test token verification with invalid token (should fail gracefully)
puts "\n4. Testing token verification error handling..."
begin
  result = FirebaseAuthService.verify_token("invalid.token.here")
  puts "   ⚠ Unexpected success with invalid token"
rescue AuthenticationExceptions::FirebaseTokenVerificationError => e
  puts "   ✓ Invalid token properly rejected with FirebaseTokenVerificationError"
rescue AuthenticationExceptions::MissingTokenError => e
  puts "   ✓ Missing token properly rejected with MissingTokenError"
rescue StandardError => e
  puts "   ⚠ Unexpected error type: #{e.class.name} - #{e.message}"
end

# Test 5: Test with empty token
puts "\n5. Testing empty token handling..."
begin
  result = FirebaseAuthService.verify_token("")
  puts "   ⚠ Unexpected success with empty token"
rescue AuthenticationExceptions::MissingTokenError => e
  puts "   ✓ Empty token properly rejected with MissingTokenError"
rescue StandardError => e
  puts "   ⚠ Unexpected error type: #{e.class.name} - #{e.message}"
end

puts "\n" + "=" * 50
puts "Firebase configuration test completed!"
puts "\nYour Firebase configuration is properly set up for:"
puts "- Project: travelgator"
puts "- Service Account: <EMAIL>"
puts "- Authentication endpoint: /api/auth/authenticate"
puts "\nTo use this in your client application, send Firebase ID tokens to the authenticate endpoint."
