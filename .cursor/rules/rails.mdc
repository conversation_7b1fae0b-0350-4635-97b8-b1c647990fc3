---
description:
globs:
alwaysApply: true
---
# TravelGator Backend Project Rules
# This file defines the rules and standards for the Rails/Mongoid project

architecture:
  - this is a mongoid project instead of activerecord project
  - this is a project which serves resultful API primarily. Try to return API errors with clarity

# Mongoid Best Practices
mongoid:
  # Model Structure
  models:
    - Include Documentable concern for all models (provides Mongoid::Document, Mongoid::Timestamps, and error handling)
    - Use field types explicitly
    - Use embeds_many/embeds_one for nested documents
    - Use references_many/references_one for relationships
    - Use Rails validations for basic checks (presence, format, inclusion)
    - Use add_error only for complex business rules
    - Use with_error for chaining error messages
    - Name validation methods specifically (e.g., validate_deactivation, validate_payment)
    example: |
      # Good - Using specific validation methods
      class User
        include Documentable  # Provides Mongoid::Document, Mongoid::Timestamps, and error methods

        field :email, type: String
        field :name, type: String
        field :status, type: String, default: "active"

        # Use Rails validations for basic checks
        validates :email, presence: true, format: { with: URI::MailTo::EMAIL_REGEXP }
        validates :name, presence: true
        validates :status, inclusion: { in: %w[active inactive] }

        # Specific validation for deactivation
        def validate_deactivation
          return false if errors.present?

          if has_active_bookings?
            add_error(:status, "Cannot deactivate user with active bookings")
            return
          end

          errors.present?
        end

        # Example of using with_error for chaining
        def deactivate
          return with_error(:status, "Cannot deactivate") unless validate_deactivation
          update(status: "inactive")
        end
      end
  update:
    - Use update instead of set when updating document.
    - Update and create model should always followed by check errors and handlings to prevent silent failure
    example: |
      user.update(email: '<EMAIL>')

      if user.errors.present?
        # handle error here if update failed

# Error Handling Rules
error_handling:
  # Model Error Handling
  models:
    rules:
      - Use Rails validations for basic checks (presence, format, inclusion)
      - Use add_error only for complex business rules
      - Use with_error for chaining error messages
      - Name validation methods specifically
      - Don't raise exceptions in models
      - Return false if validation fails
      - Fail fast by validating critical fields first
      - Chain validations in order of importance
    example: |
      class Booking
        include Documentable

        field :booking_date, type: Date
        field :status, type: String, default: "pending"
        field :amount, type: Float
        field :payment_status, type: String

        # Critical validations first
        validates :booking_date, presence: true
        validates :amount, presence: true, numericality: { greater_than: 0 }
        validates :status, inclusion: { in: %w[pending confirmed cancelled] }
        validates :payment_status, inclusion: { in: %w[pending paid failed] }

        # Specific validation for payment - fails fast on critical checks
        def validate_payment
          return false if errors.present?
          return with_error(:payment_status, "Payment already failed") if payment_failed?
          return with_error(:amount, "Amount must be greater than 0") if amount.to_f <= 0
          return with_error(:status, "Cannot confirm booking without payment") if confirmed? && !payment_received?

          errors.present?
        end

        # Specific validation for cancellation - fails fast on critical checks
        def validate_cancellation
          return false if errors.present?
          return with_error(:status, "Cannot cancel completed booking") if completed?
          return with_error(:payment_status, "Cannot cancel paid booking") if payment_paid?

          errors.present?
        end

        # Example of using with_error for chaining - fails fast on each step
        def confirm
          return with_error(:status, "Payment required") unless validate_payment
          return with_error(:status, "Invalid booking date") unless validate_booking_date
          update(status: "confirmed")
        end

        def cancel
          return with_error(:status, "Cannot cancel") unless validate_cancellation
          return with_error(:status, "Refund required") unless process_refund
          update(status: "cancelled")
        end
      end

  # Service Error Handling
  services:
    rules:
      - Use simple error messages instead of custom exceptions
      - Use model error messages when possible (e.g., model.errors.full_messages)
      - Keep error messages clear and specific to the operation
      - Log errors with context for debugging
      - Handle complex operations and validations
      - Manage transactions and side effects
      - Keep services focused and single-purpose
      - Fail fast by validating critical inputs first
      - Chain operations in order of importance
    example: |
      class OrderService
        def create_order(params)
          # Fail fast on critical validations
          raise "Missing user_id" if params[:user_id].blank?
          raise "Missing items" if params[:items].blank?

          # Create and validate order
          order = Order.new(params)
          raise order.errors.full_messages.join(", ") unless order.valid?

          # Fail fast on payment validation
          raise order.errors.full_messages.join(", ") unless order.validate_payment

          # Process in order of importance
          process_payment(params)
          create_order_record(order)
          send_notifications
        end

        def update_order_status(order_id, status)
          # Fail fast on critical checks
          raise "Invalid status" unless %w[pending confirmed cancelled].include?(status)

          order = Order.find(order_id)
          raise "Order not found" unless order

          # Fail fast on status validation
          raise order.errors.full_messages.join(", ") unless order.validate_status_change(status)

          if order.update(status: status)
            handle_status_change(order)
          else
            raise order.errors.full_messages.join(", ")
          end
        end

        private

        def process_payment(params)
          # Fail fast on payment validation
          raise "Invalid payment amount" if params[:amount].to_f <= 0
          raise "Payment method required" if params[:payment_method].blank?

          # Payment processing logic
          raise "Payment failed" if payment_failed?
        end

        def create_order_record(order)
          # Fail fast on critical checks
          raise "Order already exists" if order.persisted?
          raise "Invalid order data" unless order.valid?

          # Order creation logic
          raise "Failed to create order record" unless order.save
        end

        def send_notifications
          # Notification logic
        end
      end

# Controller Rules
controllers:
  - Keep controllers thin
    - Move business logic to services, also evaluate if its overkill and justify creating new service. Please acknowledge you are observing this rules
    - When handling logics from services, always rescue exception thrown by service and return the error as response
  - Handle only request/response flow
  - Validate input parameters
  - Return consistent JSON responses
  example: |
    # Good - Thin controller with service
    class OrdersController < ApplicationController
      def create
        order = OrderService.new.create_order(order_params)
        render json: { order: order_data(order) }
      rescue StandardError => e
        render json: { error: e.message }, status: :unprocessable_entity
      end
    end

    # Bad - Business logic in controller
    class OrdersController < ApplicationController
      def create
        order = Order.new(order_params)
        if order.validate_payment && order.process_payment
          order.calculate_totals
          order.send_notifications
          order.save
          render json: { order: order }
        else
          render json: { error: order.errors }
        end
      end
    end

# Service Layer Rules
services:
  - Use service objects for business logic
  - Handle complex operations and validations
  - Manage transactions and side effects
  - Use simple error messages instead of custom exceptions (e.g., raise "Invalid token" instead of TokenError)
  - Keep services focused and single-purpose
  - Fail fast by validating critical inputs first
  - Chain operations in order of importance
  example: |
    class OrderService
      def create_order(params)
        # Fail fast on critical validations
        raise "Missing user_id" if params[:user_id].blank?
        raise "Missing items" if params[:items].blank?

        # Create and validate order
        order = Order.new(params)
        raise order.errors.full_messages.join(", ") unless order.valid?

        # Fail fast on payment validation
        raise order.errors.full_messages.join(", ") unless order.validate_payment

        # Process in order of importance
        process_payment(params)
        create_order_record(order)
        send_notifications
      end

      def update_order_status(order_id, status)
        # Fail fast on critical checks
        raise "Invalid status" unless %w[pending confirmed cancelled].include?(status)

        order = Order.find(order_id)
        raise "Order not found" unless order

        # Fail fast on status validation
        raise order.errors.full_messages.join(", ") unless order.validate_status_change(status)

        if order.update(status: status)
          handle_status_change(order)
        else
          raise order.errors.full_messages.join(", ")
        end
      end

      private

      def process_payment(params)
        # Fail fast on payment validation
        raise "Invalid payment amount" if params[:amount].to_f <= 0
        raise "Payment method required" if params[:payment_method].blank?

        # Payment processing logic
        raise "Payment failed" if payment_failed?
      end

      def create_order_record(order)
        # Fail fast on critical checks
        raise "Order already exists" if order.persisted?
        raise "Invalid order data" unless order.valid?

        # Order creation logic
        raise "Failed to create order record" unless order.save
      end

      def send_notifications
        # Notification logic
      end
    end

# API Response Rules
api:
  responses:
    - Use consistent response format
    - Include error details
    - Use appropriate HTTP status codes
    example: |
      # Success Response
      render json: {
        data: @booking,
        meta: { total: @bookings.count }
      }, status: :ok

      # Error Response
      render json: {
        error: error.message
      }, status: :unprocessable_entity

# Code Organization
organization:
  models:
    - app/models/
    - app/models/concerns/  # Only for truly shared code
  services:
    - app/services/
  exceptions:
    - app/exceptions/

# Security Rules
security:
  - Validate all inputs
  - Use strong parameters
  - Implement proper authentication
  - Sanitize user input
  example: |
    # Good
    def user_params
      params.require(:user).permit(:name, :email)
    end

    # Bad
    User.create(params[:user]) # Don't use params directly