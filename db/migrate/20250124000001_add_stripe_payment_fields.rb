class AddStripePaymentFields < ActiveRecord::Migration[7.0]
  def change
    # Add Stripe customer ID to users
    add_column :users, :stripe_customer_id, :string
    add_index :users, :stripe_customer_id

    # Add Stripe payment fields to orders
    add_column :orders, :stripe_payment_intent_id, :string
    add_column :orders, :payment_status, :string, default: 'pending'
    add_column :orders, :order_type, :string, default: 'cart_checkout'
    
    add_index :orders, :stripe_payment_intent_id
    add_index :orders, :payment_status
    add_index :orders, :order_type

    # Remove Shopify specific fields (optional - for clean migration)
    # remove_column :orders, :shopify_order_id, :string
    # remove_column :orders, :shopify_checkout_url, :string
  end
end
