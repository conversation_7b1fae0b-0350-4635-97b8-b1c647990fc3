require "spec_helper"

RSpec.describe CartService do
  let(:user) { create_user }
  let(:service) { CartService.new(user) }
  let(:shopify_service) { instance_double(ShopifyService) }
  let(:valid_item_params) do
    {
      variant_id: Faker::Number.number(digits: 10).to_s,
      product_id: Faker::Number.number(digits: 10).to_s,
      quantity: 2,
      price: 29.99,
      title: "Test Product",
    }
  end

  before do
    allow(ShopifyService).to receive(:new).and_return(shopify_service)
  end

  describe "#find_or_create_cart" do
    context "when user has no active cart" do
      it "creates a new active cart" do
        expect {
          cart = service.find_or_create_cart
        }.to change(Cart, :count).by(1)

        cart = service.find_or_create_cart
        expect(cart.user).to eq(user)
        expect(cart.status).to eq("active")
      end
    end

    context "when user has an active cart" do
      let!(:existing_cart) { create_cart(user, status: "active") }

      it "returns the existing active cart" do
        cart = service.find_or_create_cart
        expect(cart).to eq(existing_cart)
      end

      it "does not create a new cart" do
        expect {
          service.find_or_create_cart
        }.not_to change(Cart, :count)
      end
    end

    context "when user has inactive carts" do
      let!(:inactive_cart) { create_cart(user, status: "checked_out") }

      it "creates a new active cart" do
        expect {
          cart = service.find_or_create_cart
        }.to change(Cart, :count).by(1)

        cart = service.find_or_create_cart
        expect(cart.status).to eq("active")
        expect(cart).not_to eq(inactive_cart)
      end
    end
  end

  describe "#add_item" do
    context "with valid parameters" do
      it "adds item to cart" do
        cart = service.add_item(valid_item_params)

        expect(cart.items.length).to eq(1)
        expect(cart.items.first["variant_id"]).to eq(valid_item_params[:variant_id])
        expect(cart.items.first["product_id"]).to eq(valid_item_params[:product_id])
        expect(cart.items.first["quantity"]).to eq(valid_item_params[:quantity])
        expect(cart.items.first["price"]).to eq(valid_item_params[:price])
        expect(cart.items.first["title"]).to eq(valid_item_params[:title])
      end

      it "calculates total price correctly" do
        cart = service.add_item(valid_item_params)
        expected_total = valid_item_params[:price] * valid_item_params[:quantity]
        expect(cart.total_price).to eq(expected_total)
      end

      it "creates cart if it doesn't exist" do
        expect {
          service.add_item(valid_item_params)
        }.to change(Cart, :count).by(1)
      end

      context "when item already exists in cart" do
        before do
          service.add_item(valid_item_params)
        end

        it "increases quantity of existing item" do
          cart = service.add_item(valid_item_params)
          expect(cart.items.length).to eq(1)
          expect(cart.items.first["quantity"]).to eq(valid_item_params[:quantity] * 2)
        end

        it "updates total price correctly" do
          cart = service.add_item(valid_item_params)
          expected_total = valid_item_params[:price] * (valid_item_params[:quantity] * 2)
          expect(cart.total_price).to eq(expected_total)
        end
      end
    end

    context "with missing parameters" do
      it "raises error for missing variant_id" do
        params = valid_item_params.except(:variant_id)
        expect {
          service.add_item(params)
        }.to raise_error("Missing required parameters: variant_id")
      end

      it "raises error for missing product_id" do
        params = valid_item_params.except(:product_id)
        expect {
          service.add_item(params)
        }.to raise_error("Missing required parameters: product_id")
      end

      it "raises error for missing quantity" do
        params = valid_item_params.except(:quantity)
        expect {
          service.add_item(params)
        }.to raise_error("Missing required parameters: quantity")
      end

      it "raises error for missing price" do
        params = valid_item_params.except(:price)
        expect {
          service.add_item(params)
        }.to raise_error("Missing required parameters: price")
      end

      it "raises error for multiple missing parameters" do
        params = valid_item_params.except(:variant_id, :price)
        expect {
          service.add_item(params)
        }.to raise_error("Missing required parameters: variant_id, price")
      end
    end

    context "with invalid parameters" do
      it "raises error for zero quantity" do
        params = valid_item_params.merge(quantity: 0)
        expect {
          service.add_item(params)
        }.to raise_error("Quantity must be greater than 0")
      end

      it "raises error for negative quantity" do
        params = valid_item_params.merge(quantity: -1)
        expect {
          service.add_item(params)
        }.to raise_error("Quantity must be greater than 0")
      end

      it "raises error for zero price" do
        params = valid_item_params.merge(price: 0)
        expect {
          service.add_item(params)
        }.to raise_error("Price must be greater than 0")
      end

      it "raises error for negative price" do
        params = valid_item_params.merge(price: -10.0)
        expect {
          service.add_item(params)
        }.to raise_error("Price must be greater than 0")
      end
    end
  end

  describe "#remove_item" do
    let!(:cart) { create_cart(user, items: [valid_item_params]) }

    context "with valid variant_id" do
      it "removes item from cart" do
        service.remove_item(valid_item_params[:variant_id])

        cart.reload
        expect(cart.items).to be_empty
        expect(cart.total_price).to eq(0.0)
      end

      it "updates total price correctly" do
        # Add another item first
        second_item = valid_item_params.merge(variant_id: "different_variant", price: 15.0)
        service.add_item(second_item)

        service.remove_item(valid_item_params[:variant_id])

        cart.reload
        expected_total = second_item[:price] * second_item[:quantity]
        expect(cart.total_price).to eq(expected_total)
      end
    end

    context "with missing variant_id" do
      it "raises error for nil variant_id" do
        expect {
          service.remove_item(nil)
        }.to raise_error("Variant ID is required")
      end

      it "raises error for blank variant_id" do
        expect {
          service.remove_item("")
        }.to raise_error("Variant ID is required")
      end

      it "raises error for whitespace variant_id" do
        expect {
          service.remove_item("   ")
        }.to raise_error("Variant ID is required")
      end
    end

    context "with non-existent variant_id" do
      it "does not raise error and returns cart" do
        cart = service.remove_item("non_existent_variant")
        expect(cart).to be_present
        expect(cart.items.length).to eq(1)
      end
    end
  end

  describe "#update_item_quantity" do
    let!(:cart) { create_cart(user, items: [valid_item_params]) }

    context "with valid parameters" do
      it "updates item quantity" do
        new_quantity = 5
        service.update_item_quantity(valid_item_params[:variant_id], new_quantity)

        cart.reload
        expect(cart.items.first["quantity"]).to eq(new_quantity)
      end

      it "updates total price correctly" do
        new_quantity = 3
        service.update_item_quantity(valid_item_params[:variant_id], new_quantity)

        cart.reload
        expected_total = valid_item_params[:price] * new_quantity
        expect(cart.total_price).to eq(expected_total)
      end
    end

    context "with missing variant_id" do
      it "raises error for nil variant_id" do
        expect {
          service.update_item_quantity(nil, 2)
        }.to raise_error("Variant ID is required")
      end

      it "raises error for blank variant_id" do
        expect {
          service.update_item_quantity("", 2)
        }.to raise_error("Variant ID is required")
      end
    end

    context "with invalid quantity" do
      it "raises error for zero quantity" do
        expect {
          service.update_item_quantity(valid_item_params[:variant_id], 0)
        }.to raise_error("Quantity must be greater than 0")
      end

      it "raises error for negative quantity" do
        expect {
          service.update_item_quantity(valid_item_params[:variant_id], -1)
        }.to raise_error("Quantity must be greater than 0")
      end
    end

    context "with non-existent variant_id" do
      it "does not raise error and returns cart" do
        cart = service.update_item_quantity("non_existent_variant", 5)
        expect(cart).to be_present
        expect(cart.items.first["quantity"]).to eq(valid_item_params[:quantity])
      end
    end
  end

  describe "#process_checkout" do
    let!(:cart) { create_cart(user, items: mock_cart_items) }
    let(:checkout_response) { mock_shopify_checkout }

    before do
      allow(shopify_service).to receive(:create_checkout).with(cart.items, user.shopify_customer_id).and_return(checkout_response)
    end

    context "with successful checkout" do
      it "creates order and updates cart status" do
        expect {
          result = service.process_checkout
        }.to change(Order, :count).by(1)

        cart.reload
        expect(cart.status).to eq("checked_out")
      end

      it "returns checkout data" do
        result = service.process_checkout

        expect(result).to include(:message, :data)
        expect(result[:data]).to include(
          :checkout_url,
          :order_id,
          :shopify_order_id,
          :shopify_order_name,
          :total_price,
          :currency,
          :status,
          :order_type
        )
      end

      it "creates order with correct attributes" do
        result = service.process_checkout
        order = Order.last

        expect(order.user).to eq(user)
        expect(order.shopify_order_id).to eq(checkout_response["id"])
        expect(order.shopify_checkout_url).to eq(checkout_response["webUrl"])
        expect(order.total_price).to eq(cart.total_price)
        expect(order.currency).to eq(cart.currency)
        expect(order.items).to eq(cart.items)
      end

      context "when checkout is a draft order" do
        let(:draft_checkout) { checkout_response.merge("isDraft" => true) }

        before do
          allow(shopify_service).to receive(:create_checkout).and_return(draft_checkout)
        end

        it "sets order status to draft" do
          result = service.process_checkout
          order = Order.last

          expect(order.status).to eq("draft")
          expect(result[:data][:status]).to eq("draft")
          expect(result[:data][:order_type]).to eq("draft_order")
        end
      end

      context "when checkout is completed" do
        let(:completed_checkout) { checkout_response.merge("name" => "Order #123456") }

        before do
          allow(shopify_service).to receive(:create_checkout).and_return(completed_checkout)
        end

        it "sets order status to completed" do
          result = service.process_checkout
          order = Order.last

          expect(order.status).to eq("completed")
          expect(result[:data][:status]).to eq("completed")
          expect(result[:data][:order_type]).to eq("shopify_order")
        end
      end

      context "when checkout is pending" do
        let(:pending_checkout) { checkout_response.except("name") }

        before do
          allow(shopify_service).to receive(:create_checkout).and_return(pending_checkout)
        end

        it "sets order status to pending" do
          result = service.process_checkout
          order = Order.last

          expect(order.status).to eq("pending")
          expect(result[:data][:status]).to eq("pending")
          expect(result[:data][:order_type]).to eq("checkout_cart")
        end
      end
    end

    context "when Shopify checkout fails" do
      before do
        allow(shopify_service).to receive(:create_checkout).and_return(nil)
      end

      it "raises error" do
        expect {
          service.process_checkout
        }.to raise_error("Unable to process checkout request")
      end
    end

    context "when order creation fails" do
      before do
        allow(shopify_service).to receive(:create_checkout).and_return(checkout_response)
        # Create an order that will fail validation
        allow(Order).to receive(:create).and_return(
          double(persisted?: false, errors: double(full_messages: ["Shopify order ID is invalid"]))
        )
      end

      it "raises error with order validation messages" do
        expect {
          service.process_checkout
        }.to raise_error("Shopify order ID is invalid")
      end
    end
  end
end
