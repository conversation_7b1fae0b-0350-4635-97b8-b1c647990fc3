require 'spec_helper'

RSpec.describe StripePaymentService, type: :service do
  let(:user) { create_user }
  let(:buyer_ip) { '***********' }

  describe '#format_shipping_address' do
    let(:service) { described_class.allocate }

    before do
      # Set up the service instance without calling initialize to avoid Stripe API calls
      service.instance_variable_set(:@user, user)
      service.instance_variable_set(:@buyer_ip, buyer_ip)
    end

    context 'with string keys' do
      let(:address_with_string_keys) do
        {
          'name' => '<PERSON>',
          'line1' => '123 Main St',
          'line2' => 'Apt 4B',
          'city' => 'New York',
          'state' => 'NY',
          'postal_code' => '10001',
          'country' => 'US',
          'phone' => '+1234567890'
        }
      end

      it 'formats address correctly' do
        result = service.send(:format_shipping_address, address_with_string_keys)
        
        expect(result).to eq({
          address: {
            line1: '123 Main St',
            line2: 'Apt 4B',
            city: 'New York',
            state: 'NY',
            postal_code: '10001',
            country: 'US'
          },
          name: '<PERSON>',
          phone: '+1234567890'
        })
      end
    end

    context 'with symbol keys' do
      let(:address_with_symbol_keys) do
        {
          name: '<PERSON>',
          line1: '456 Oak Ave',
          city: 'Los Angeles',
          state: 'CA',
          postal_code: '90210',
          country: 'US'
        }
      end

      it 'formats address correctly' do
        result = service.send(:format_shipping_address, address_with_symbol_keys)
        
        expect(result).to eq({
          address: {
            line1: '456 Oak Ave',
            line2: nil,
            city: 'Los Angeles',
            state: 'CA',
            postal_code: '90210',
            country: 'US'
          },
          name: 'Jane Smith',
          phone: nil
        })
      end
    end

    context 'with partial address data' do
      let(:partial_address) do
        {
          'name' => 'Bob Wilson',
          'line1' => '789 Pine St',
          'city' => 'Chicago',
          'state' => 'IL',
          'postal_code' => '60601',
          'country' => 'US'
          # Missing line2 and phone
        }
      end

      it 'handles missing optional fields correctly' do
        result = service.send(:format_shipping_address, partial_address)

        expect(result).to eq({
          address: {
            line1: '789 Pine St',
            line2: nil,
            city: 'Chicago',
            state: 'IL',
            postal_code: '60601',
            country: 'US'
          },
          name: 'Bob Wilson',
          phone: nil
        })
      end
    end
  end

  describe '#confirm_payment' do
    let(:payment_intent_id) { 'pi_test_123' }
    let(:payment_method_id) { 'pm_test_456' }
    let(:mock_user) { double('User', id: 'user_123') }
    let(:service) { described_class.allocate }

    before do
      # Set up the service instance without calling initialize to avoid Stripe API calls
      service.instance_variable_set(:@user, mock_user)
      service.instance_variable_set(:@buyer_ip, buyer_ip)

      # Mock Stripe API key setup
      allow(ENV).to receive(:[]).with('STRIPE_SECRET_KEY').and_return('sk_test_123')
      allow(ENV).to receive(:[]).with('FRONTEND_URL').and_return('https://test.travelgator.com')
      allow(Stripe).to receive(:api_key=)
    end

    context 'when payment_method_id is blank' do
      it 'raises an error for missing payment method' do
        expect {
          service.confirm_payment(payment_intent_id, nil)
        }.to raise_error('Payment method ID is required. Use Stripe Payment Sheet to create payment method.')
      end

      it 'raises an error for empty string payment method' do
        expect {
          service.confirm_payment(payment_intent_id, '')
        }.to raise_error('Payment method ID is required. Use Stripe Payment Sheet to create payment method.')
      end
    end

    context 'when payment intent belongs to different user' do
      let(:mock_payment_intent) do
        double('PaymentIntent',
          id: payment_intent_id,
          metadata: double('metadata', user_id: 'different_user_id')
        )
      end

      before do
        allow(Stripe::PaymentIntent).to receive(:retrieve).with(payment_intent_id).and_return(mock_payment_intent)
      end

      it 'raises unauthorized access error' do
        expect {
          service.confirm_payment(payment_intent_id, payment_method_id)
        }.to raise_error('Unauthorized payment intent access')
      end
    end
  end
end
