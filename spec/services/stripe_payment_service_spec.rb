require 'spec_helper'

RSpec.describe StripePaymentService, type: :service do
  let(:user) { create_user }
  let(:buyer_ip) { '***********' }
  let(:service) { described_class.allocate } # Create instance without calling initialize

  before do
    # Set up the service instance without calling initialize
    service.instance_variable_set(:@user, user)
    service.instance_variable_set(:@buyer_ip, buyer_ip)
  end

  describe '#format_shipping_address' do
    context 'with string keys' do
      let(:address_with_string_keys) do
        {
          'name' => '<PERSON>',
          'line1' => '123 Main St',
          'line2' => 'Apt 4B',
          'city' => 'New York',
          'state' => 'NY',
          'postal_code' => '10001',
          'country' => 'US',
          'phone' => '+1234567890'
        }
      end

      it 'formats address correctly' do
        result = service.send(:format_shipping_address, address_with_string_keys)
        
        expect(result).to eq({
          address: {
            line1: '123 Main St',
            line2: 'Apt 4B',
            city: 'New York',
            state: 'NY',
            postal_code: '10001',
            country: 'US'
          },
          name: '<PERSON>',
          phone: '+1234567890'
        })
      end
    end

    context 'with symbol keys' do
      let(:address_with_symbol_keys) do
        {
          name: '<PERSON>',
          line1: '456 Oak Ave',
          city: 'Los Angeles',
          state: 'CA',
          postal_code: '90210',
          country: 'US'
        }
      end

      it 'formats address correctly' do
        result = service.send(:format_shipping_address, address_with_symbol_keys)
        
        expect(result).to eq({
          address: {
            line1: '456 Oak Ave',
            line2: nil,
            city: 'Los Angeles',
            state: 'CA',
            postal_code: '90210',
            country: 'US'
          },
          name: 'Jane Smith',
          phone: nil
        })
      end
    end

    context 'with partial address data' do
      let(:partial_address) do
        {
          'name' => 'Bob Wilson',
          'line1' => '789 Pine St',
          'city' => 'Chicago',
          'state' => 'IL',
          'postal_code' => '60601',
          'country' => 'US'
          # Missing line2 and phone
        }
      end

      it 'handles missing optional fields correctly' do
        result = service.send(:format_shipping_address, partial_address)

        expect(result).to eq({
          address: {
            line1: '789 Pine St',
            line2: nil,
            city: 'Chicago',
            state: 'IL',
            postal_code: '60601',
            country: 'US'
          },
          name: 'Bob Wilson',
          phone: nil
        })
      end
    end
  end
end
