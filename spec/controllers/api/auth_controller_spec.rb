require 'spec_helper'

RSpec.describe "JWT Token Verification" do
  let(:user) { create_user }

  let(:valid_token) do
    JWT.encode(
      { user_id: user.id.to_s, exp: 24.hours.from_now.to_i },
      ENV["JWT_SECRET"] || "development_jwt_secret"
    )
  end

  let(:expired_token) do
    JWT.encode(
      { user_id: user.id.to_s, exp: 1.hour.ago.to_i },
      ENV["JWT_SECRET"] || "development_jwt_secret"
    )
  end

  let(:invalid_token) { "invalid.jwt.token" }

  describe "JWT token verification logic" do
    it "can decode a valid token" do
      decoded_token = JWT.decode(valid_token, ENV["JWT_SECRET"] || "development_jwt_secret", true, { algorithm: 'HS256' })
      user_data = decoded_token[0]

      expect(user_data['user_id']).to eq(user.id.to_s)
      expect(user_data['exp']).to be > Time.now.to_i
    end

    it "raises error for expired token" do
      expect {
        JWT.decode(expired_token, ENV["JWT_SECRET"] || "development_jwt_secret", true, { algorithm: 'HS256' })
      }.to raise_error(JWT::ExpiredSignature)
    end

    it "raises error for invalid token" do
      expect {
        JWT.decode(invalid_token, ENV["JWT_SECRET"] || "development_jwt_secret", true, { algorithm: 'HS256' })
      }.to raise_error(JWT::DecodeError)
    end

    it "can find user from valid token" do
      decoded_token = JWT.decode(valid_token, ENV["JWT_SECRET"] || "development_jwt_secret", true, { algorithm: 'HS256' })
      user_data = decoded_token[0]
      found_user = User.find(user_data['user_id'])

      expect(found_user.id).to eq(user.id)
      expect(found_user.email).to eq(user.email)
      expect(found_user.name).to eq(user.name)
      expect(found_user.firebase_uid).to eq(user.firebase_uid)
    end
  end

  describe "Auth controller helper methods" do
    let(:controller) { Api::AuthController.new }

    describe "#extract_token_from_header" do
      it "extracts token from Bearer authorization header" do
        mock_request = double('request')
        headers = { 'Authorization' => 'Bearer test_token_123' }
        allow(mock_request).to receive(:headers).and_return(headers)
        allow(controller).to receive(:request).and_return(mock_request)

        token = controller.send(:extract_token_from_header)
        expect(token).to eq('test_token_123')
      end

      it "returns nil when no Authorization header" do
        mock_request = double('request')
        headers = {}
        allow(mock_request).to receive(:headers).and_return(headers)
        allow(controller).to receive(:request).and_return(mock_request)

        token = controller.send(:extract_token_from_header)
        expect(token).to be_nil
      end

      it "returns nil when Authorization header doesn't start with Bearer" do
        mock_request = double('request')
        headers = { 'Authorization' => 'Basic test_token_123' }
        allow(mock_request).to receive(:headers).and_return(headers)
        allow(controller).to receive(:request).and_return(mock_request)

        token = controller.send(:extract_token_from_header)
        expect(token).to be_nil
      end
    end
  end
end
