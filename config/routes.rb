Rails.application.routes.draw do
  namespace :api do
    post "auth/authenticate", to: "auth#authenticate"
    get "auth/verify", to: "auth#verify"

    # Remove products routes - Flutter calls Shopify directly

    resource :cart, only: [:show] do
      post :add_item
      delete :remove_item
      patch :update_item
      post :checkout
      post :confirm_payment
      get :payment_status
      post :cancel_payment
      delete :clear
    end

    # Buy Now endpoints
    post "buy_now", to: "buy_now#create_checkout"
    post "buy_now/confirm_payment", to: "buy_now#confirm_payment"
    get "buy_now/payment_status", to: "buy_now#payment_status"

    resources :orders, only: [:index, :show]

    # Stripe configuration
    get "stripe_payments/config", to: "stripe_payments#config"
  end
end
