require_relative "boot"

require "rails"
# Pick the frameworks you want:
require "active_model/railtie"
require "active_job/railtie"
require "action_controller/railtie"
require "action_mailer/railtie"
require "action_view/railtie"
require "sprockets/railtie"
require "rails/test_unit/railtie"

# Require the gems listed in Gemfile
Bundler.require(*Rails.groups)

module Travelgator
  class Application < Rails::Application
    # Initialize configuration defaults for originally generated Rails version
    config.load_defaults 5.2

    # Remove ActiveRecord
    Object.send(:remove_const, :ActiveRecord)

    # Basic configurations
    config.time_zone = "Singapore"
    # config/initializers/mongoid.rb
    Mongoid.logger = Logger.new($stdout, :debug)
    Mongo::Logger.logger.level = Logger::DEBUG

    # Autoload paths - ensure services are loaded
    config.autoload_paths += %W(#{config.root}/app/services)
    config.autoload_paths += %W(#{config.root}/config/routes)
  end
end
