# TravelGator Backend Documentation

## 📚 Documentation Overview

Welcome to the TravelGator backend documentation. This Rails API provides authentication, cart management, and Stripe payment processing for the TravelGator Flutter application.

## 🚀 Quick Start

1. **[Stripe Checkout Guide](stripe_checkout_guide.md)** - **START HERE** 
   - Complete guide for implementing Stripe payments
   - Step-by-step API usage examples
   - Flutter integration examples
   - Error handling and testing

## 📖 Core Documentation

### Payment System
- **[Stripe Checkout Guide](stripe_checkout_guide.md)** - Main implementation guide
- **[Stripe API Reference](stripe_api_reference.md)** - API endpoints and responses
- **[Stripe Client Integration](stripe_client_integration.md)** - Flutter client integration
- **[Stripe Testing Guide](stripe_testing_guide.md)** - Testing strategies and test cards
- **[Implementation Checklist](implementation_checklist.md)** - Development checklist

### Authentication
- **[Auth Verify Endpoint](auth_verify_endpoint.md)** - JWT authentication system

## 🏗️ System Architecture

```
Flutter App ←→ Rails API ←→ Stripe API
     ↓              ↓
   UI/UX      Database & Logic
```

### Key Components
- **Authentication**: JWT-based user authentication
- **Cart Management**: Session-based shopping cart
- **Payment Processing**: Stripe Payment Intents
- **Order Tracking**: Order status and history

## 🔧 API Endpoints

### Authentication
```
POST /api/auth/authenticate  # Login and get JWT token
GET  /api/auth/verify        # Verify JWT token
```

### Cart Management
```
GET    /api/cart              # Get current cart
POST   /api/cart/add_item     # Add item to cart
DELETE /api/cart/remove_item  # Remove item from cart  
PATCH  /api/cart/update_item  # Update item quantity
DELETE /api/cart/clear        # Clear cart
```

### Payment Processing
```
POST /api/cart/checkout         # Create payment intent for cart
POST /api/cart/confirm_payment  # Confirm payment
GET  /api/cart/payment_status   # Check payment status
POST /api/cart/cancel_payment   # Cancel payment

POST /api/buy_now               # Create payment intent for single item
POST /api/buy_now/confirm_payment # Confirm buy now payment
GET  /api/buy_now/payment_status  # Check buy now payment status
```

### Orders
```
GET /api/orders     # Get user's orders
GET /api/orders/:id # Get specific order
```

## 💳 Payment Flow

### Cart Checkout
1. **Add items** to cart via `/api/cart/add_item`
2. **Create payment intent** via `/api/cart/checkout`
3. **Process payment** using Flutter Stripe SDK
4. **Check status** via `/api/cart/payment_status`
5. **Cart automatically cleared** on success

### Buy Now
1. **Create payment intent** via `/api/buy_now`
2. **Process payment** using Flutter Stripe SDK  
3. **Check status** via `/api/buy_now/payment_status`

## 🧪 Testing

### Test Environment
- Use Stripe test API keys (`pk_test_...`, `sk_test_...`)
- Test with provided test card numbers
- No real money is processed in test mode

### Test Cards
- **Success**: `****************`
- **Declined**: `****************`  
- **3D Secure**: `****************`

See [Stripe Testing Guide](stripe_testing_guide.md) for complete testing documentation.

## 🔐 Security

### Authentication
- JWT tokens for API access
- User ownership verification for all operations
- Payment intent ownership validation

### Payment Security
- PCI compliance through Stripe
- No card data stored in backend
- Stripe handles all sensitive payment data

## 🚨 Error Handling

### Common Error Responses
```json
{
  "success": false,
  "error": "Error message describing the issue"
}
```

### HTTP Status Codes
- `200` - Success
- `401` - Unauthorized (invalid/missing JWT)
- `422` - Validation error
- `500` - Server error

## 📋 Development Workflow

1. **Setup**: Configure Stripe API keys in environment
2. **Development**: Use test API keys and test cards
3. **Testing**: Run test suite and manual testing
4. **Production**: Switch to live API keys

## 🔗 External References

- [Stripe API Documentation](https://stripe.com/docs/api)
- [Stripe Flutter SDK](https://pub.dev/packages/flutter_stripe)
- [Rails API Documentation](https://guides.rubyonrails.org/api_app.html)

## 📞 Support

For questions or issues:
1. Check the relevant documentation file
2. Review error messages and logs
3. Test with Stripe test cards
4. Verify API key configuration

---

**💡 Pro Tip**: Start with the [Stripe Checkout Guide](stripe_checkout_guide.md) for a complete walkthrough of the payment system implementation. 