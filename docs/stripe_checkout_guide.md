# Stripe Checkout Guide - TravelGator Backend

## 📋 Overview

This guide covers how to use the Stripe payment intent system for processing payments in the TravelGator backend. The system supports two main checkout flows: **Cart Checkout** for multiple items and **Buy Now** for single item purchases.

## 🏗️ Architecture

```
Flutter App → Rails API → Stripe API → Rails API → Flutter App
     ↓                                                    ↑
Payment UI ←------ Polling for Status ←------ Payment Intent
```

### Key Components
- **StripePaymentService**: Core service handling Stripe operations
- **CartsController**: Cart checkout endpoints
- **BuyNowController**: Single item purchase endpoints
- **Order Model**: Tracks payment and order status
- **User Model**: Stores Stripe customer information

## 🔑 Prerequisites

### Backend Setup
- Stripe API keys configured in environment variables
- User authentication system (JWT)
- Mongoid database with Order and User models

### Environment Variables
```bash
# Development
STRIPE_SECRET_KEY=sk_test_...
STRIPE_PUBLISHABLE_KEY=pk_test_...

# Production  
STRIPE_SECRET_KEY=sk_live_...
STRIPE_PUBLISHABLE_KEY=pk_live_...
```

### Flutter Dependencies
```yaml
dependencies:
  flutter_stripe: ^11.1.0
  dio: ^5.4.0
  provider: ^6.1.1
```

## 🛒 Cart Checkout Flow

### Step 1: Add Items to Cart

**Endpoint:** `POST /api/cart/add_item`

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "variant_id": "gid://shopify/ProductVariant/123456",
  "quantity": 2,
  "price": 25.99,
  "title": "Travel Backpack",
  "image_url": "https://example.com/image.jpg"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "items": [
      {
        "variant_id": "gid://shopify/ProductVariant/123456",
        "quantity": 2,
        "price": 25.99,
        "title": "Travel Backpack"
      }
    ],
    "total_price": 51.98,
    "currency": "USD",
    "items_count": 2
  }
}
```

### Step 2: Create Payment Intent

**Endpoint:** `POST /api/cart/checkout`

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "shipping_address": {
    "name": "John Doe",
    "line1": "123 Main St",
    "line2": "Apt 4B",
    "city": "New York",
    "state": "NY", 
    "postal_code": "10001",
    "country": "US",
    "phone": "+1234567890"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "client_secret": "pi_3MtwBwLkdIwHu7ix28a3tqPa_secret_xxx",
    "payment_intent_id": "pi_3MtwBwLkdIwHu7ix28a3tqPa",
    "order_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "amount": 51.98,
    "currency": "USD"
  }
}
```

### Step 3: Process Payment (Flutter)

```dart
// Initialize Stripe payment sheet
await Stripe.instance.initPaymentSheet(
  paymentSheetParameters: SetupPaymentSheetParameters(
    paymentIntentClientSecret: clientSecret,
    merchantDisplayName: 'TravelGator',
    style: ThemeMode.system,
    appearance: PaymentSheetAppearance(
      colors: PaymentSheetAppearanceColors(
        primary: Color(0xFF1976D2),
      ),
    ),
  ),
);

// Present payment sheet to user
try {
  await Stripe.instance.presentPaymentSheet();
  // Payment successful - check status
  await checkPaymentStatus(paymentIntentId);
} on StripeException catch (e) {
  switch (e.error.code) {
    case FailureCode.Canceled:
      // User canceled payment
      print('Payment canceled by user');
      break;
    case FailureCode.Failed:
      // Payment failed
      print('Payment failed: ${e.error.localizedMessage}');
      break;
  }
}
```

### Step 4: Check Payment Status

**Endpoint:** `GET /api/cart/payment_status`

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Query Parameters:**
```
payment_intent_id=pi_3MtwBwLkdIwHu7ix28a3tqPa
```

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "succeeded",
    "order_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "amount_received": 5198,
    "charges": [
      {
        "id": "ch_3MtwBwLkdIwHu7ix28a3tqPa",
        "amount": 5198,
        "status": "succeeded",
        "created": 1680800504,
        "receipt_url": "https://pay.stripe.com/receipts/xxx"
      }
    ]
  }
}
```

## 🛍️ Buy Now Flow

### Create Payment Intent for Single Item

**Endpoint:** `POST /api/buy_now`

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "variant_id": "gid://shopify/ProductVariant/123456",
  "quantity": 1,
  "price": 29.99,
  "product_name": "Travel Mug"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "client_secret": "pi_3MtwBwLkdIwHu7ix28a3tqPa_secret_xxx",
    "payment_intent_id": "pi_3MtwBwLkdIwHu7ix28a3tqPa",
    "order_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "amount": 29.99,
    "currency": "USD"
  }
}
```

**Follow the same payment processing steps as cart checkout.**

## 🔄 Alternative: Custom Payment Form

### Confirm Payment with Payment Method

**Endpoint:** `POST /api/cart/confirm_payment` or `POST /api/buy_now/confirm_payment`

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "payment_intent_id": "pi_3MtwBwLkdIwHu7ix28a3tqPa",
  "payment_method_id": "pm_1234567890"
}
```

**Response (Success):**
```json
{
  "success": true,
  "data": {
    "status": "succeeded",
    "client_secret": "pi_3MtwBwLkdIwHu7ix28a3tqPa_secret_xxx",
    "next_action": null,
    "payment_intent_id": "pi_3MtwBwLkdIwHu7ix28a3tqPa"
  }
}
```

**Response (3D Secure Required):**
```json
{
  "success": true,
  "data": {
    "status": "requires_action",
    "client_secret": "pi_3MtwBwLkdIwHu7ix28a3tqPa_secret_xxx",
    "next_action": {
      "type": "use_stripe_sdk",
      "use_stripe_sdk": {
        "type": "three_d_secure_redirect"
      }
    },
    "payment_intent_id": "pi_3MtwBwLkdIwHu7ix28a3tqPa"
  }
}
```

### Handle 3D Secure Authentication

```dart
if (response.data['status'] == 'requires_action') {
  try {
    await Stripe.instance.handleNextAction(clientSecret);
    // Check payment status again after 3D Secure
    await checkPaymentStatus(paymentIntentId);
  } catch (e) {
    print('3D Secure authentication failed: $e');
  }
}
```

## ❌ Cancel Payment

**Endpoint:** `POST /api/cart/cancel_payment`

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "payment_intent_id": "pi_3MtwBwLkdIwHu7ix28a3tqPa"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "cancelled"
  }
}
```

## 📊 Payment Status Reference

| Status | Description | Client Action |
|--------|-------------|---------------|
| `requires_payment_method` | Payment method needed | Collect card details |
| `requires_confirmation` | Ready to confirm | Call confirm_payment |
| `requires_action` | 3D Secure needed | Handle next_action |
| `processing` | Being processed | Poll for status updates |
| `succeeded` | Payment complete | Show success message |
| `canceled` | Payment cancelled | Handle cancellation |
| `requires_capture` | Payment authorized | Auto-handled by Stripe |

## 🔐 Authentication

### Get JWT Token

**Endpoint:** `POST /api/auth/authenticate`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiJ9...",
    "user": {
      "id": "64f8a1b2c3d4e5f6a7b8c9d0",
      "email": "<EMAIL>",
      "name": "John Doe"
    }
  }
}
```

### Use Token in Requests

```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...
```

## 🧪 Testing

### Test Cards (Stripe Test Mode)

| Card Number | Result | CVC | Date |
|-------------|--------|-----|------|
| `****************` | Success | Any 3 digits | Any future date |
| `****************` | Declined | Any 3 digits | Any future date |
| `****************` | 3D Secure | Any 3 digits | Any future date |
| `****************` | Insufficient funds | Any 3 digits | Any future date |
| `****************` | Lost card | Any 3 digits | Any future date |

### Test Environment Setup

```dart
// Flutter app initialization
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Use test publishable key
  Stripe.publishableKey = 'pk_test_...';
  
  runApp(MyApp());
}
```

### Example Test Flow

```bash
# 1. Authenticate
curl -X POST http://localhost:3000/api/auth/authenticate \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'

# 2. Add item to cart
curl -X POST http://localhost:3000/api/cart/add_item \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "variant_id": "test_variant_123",
    "quantity": 1,
    "price": 10.00,
    "title": "Test Product"
  }'

# 3. Create payment intent
curl -X POST http://localhost:3000/api/cart/checkout \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{}'

# 4. Check payment status
curl -X GET "http://localhost:3000/api/cart/payment_status?payment_intent_id=pi_xxx" \
  -H "Authorization: Bearer <token>"
```

## ⚠️ Error Handling

### Common Error Responses

```json
{
  "success": false,
  "error": "Cart is empty - add items before checkout"
}
```

```json
{
  "success": false,
  "error": "Unauthorized payment intent access"
}
```

```json
{
  "success": false,
  "error": "Failed to create payment: Invalid cart item: missing variant_id"
}
```

### Flutter Error Handling

```dart
try {
  final response = await dio.post('/api/cart/checkout');
  
  if (response.data['success']) {
    final paymentData = response.data['data'];
    await processPayment(paymentData);
  } else {
    showError(response.data['error']);
  }
} on DioException catch (e) {
  if (e.response?.statusCode == 401) {
    // Redirect to login
    navigateToLogin();
  } else if (e.response?.statusCode == 422) {
    // Validation error
    showError(e.response?.data['error'] ?? 'Validation failed');
  } else {
    // Network or other error
    showError('Network error occurred');
  }
} catch (e) {
  showError('An unexpected error occurred');
}
```

## 🔄 Status Polling Implementation

### Polling Strategy

```dart
Future<PaymentResult> pollPaymentStatus(String paymentIntentId) async {
  int attempts = 0;
  const maxAttempts = 10;
  
  while (attempts < maxAttempts) {
    try {
      final response = await dio.get(
        '/api/cart/payment_status',
        queryParameters: {'payment_intent_id': paymentIntentId},
      );
      
      final status = response.data['data']['status'];
      
      if (['succeeded', 'canceled', 'failed'].contains(status)) {
        return PaymentResult.fromJson(response.data['data']);
      }
      
      // Exponential backoff: 1s, 2s, 4s, 8s, max 30s
      final delay = Duration(
        seconds: math.min(math.pow(2, attempts).toInt(), 30),
      );
      
      await Future.delayed(delay);
      attempts++;
      
    } catch (e) {
      attempts++;
      if (attempts >= maxAttempts) rethrow;
    }
  }
  
  throw Exception('Payment status polling timeout');
}
```

## 🎯 Key Features

### ✅ Automatic Cart Clearing
- Cart is automatically cleared when payment succeeds
- Cart status updated to `'checked_out'`

### ✅ User Ownership Verification
```ruby
unless payment_intent.metadata.user_id == @user.id.to_s
  raise "Unauthorized payment intent access"
end
```

### ✅ Stripe Customer Management
- Automatically creates/retrieves Stripe customers
- Stores `stripe_customer_id` in User model
- Enables payment method reuse

### ✅ Order Tracking
- Creates Order record immediately when payment intent is created
- Updates order status based on payment intent status
- Tracks payment metadata for debugging

### ✅ 3D Secure Support
- Handles `requires_action` status
- Provides `next_action` data for client handling
- Supports European Strong Customer Authentication (SCA)

### ✅ Comprehensive Logging
- Logs all payment operations
- Error context for debugging
- Payment flow tracking

## 📚 Additional Resources

### API Endpoints Summary

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/cart/add_item` | Add item to cart |
| POST | `/api/cart/checkout` | Create cart payment intent |
| POST | `/api/cart/confirm_payment` | Confirm cart payment |
| GET | `/api/cart/payment_status` | Check cart payment status |
| POST | `/api/cart/cancel_payment` | Cancel cart payment |
| POST | `/api/buy_now` | Create buy now payment intent |
| POST | `/api/buy_now/confirm_payment` | Confirm buy now payment |
| GET | `/api/buy_now/payment_status` | Check buy now payment status |

### Environment Configuration

```bash
# .env file
STRIPE_SECRET_KEY=sk_test_51Abc...
STRIPE_PUBLISHABLE_KEY=pk_test_51Abc...

# Optional: Webhook endpoint signing secret
STRIPE_WEBHOOK_SECRET=whsec_abc123...
```

### Production Checklist

- [ ] Replace test API keys with live keys
- [ ] Test with real cards in Stripe test mode
- [ ] Implement proper error monitoring
- [ ] Set up payment analytics
- [ ] Configure webhook endpoints (optional)
- [ ] Test 3D Secure flows
- [ ] Verify PCI compliance requirements
- [ ] Set up fraud detection rules
- [ ] Test refund processes
- [ ] Implement proper logging

This documentation covers the complete Stripe checkout system implementation. For additional questions or advanced use cases, refer to the Stripe API documentation or the source code in the respective service and controller files.

## 🔧 Troubleshooting

### Common Issues and Solutions

#### 1. "Payment method requires return_url" Error

**Error Message:**
```
This PaymentIntent is configured to accept payment methods enabled in your Dashboard. 
Because some of these payment methods might redirect your customer off of your page, 
you must provide a `return_url`. If you don't want to accept redirect-based payment methods, 
set `automatic_payment_methods[enabled]` to `true` and `automatic_payment_methods[allow_redirects]` to `never`.
```

**Solution:**
The system is already configured to disable redirect-based payment methods:

```ruby
automatic_payment_methods: {
  enabled: true,
  allow_redirects: 'never'  # Prevents redirect-based payment methods
}
```

This error typically occurs if you're using an older version of the code. Make sure your `StripePaymentService` includes the `allow_redirects: 'never'` setting.

#### 2. Secure Payment Method Approach

🔐 **SECURITY REQUIREMENT**: The system only accepts pre-created payment methods for PCI compliance.

**✅ Secure Payment Method (Required)**
```json
{
  "payment_intent_id": "pi_xxx",
  "payment_method_id": "pm_xxx"
}
```

**❌ Raw Card Details (REMOVED for Security)**
Card details are no longer accepted by the backend. Use Stripe Payment Sheet to create payment methods securely.
```

#### 3. 3D Secure Handling

If you receive `requires_action` status:

```dart
if (response.data['status'] == 'requires_action') {
  final nextAction = response.data['next_action'];
  if (nextAction != null) {
    await Stripe.instance.handleNextAction(clientSecret);
    // Poll for updated status
    await checkPaymentStatus(paymentIntentId);
  }
}
```

#### 4. Payment Method Creation Errors

**Error:** "Your card number is incorrect"
- Verify test card numbers are being used in development
- Check that card details are properly formatted

**Error:** "Your card's expiration date is incorrect"
- Ensure `exp_month` is 1-12
- Ensure `exp_year` is 4 digits and in the future

#### 5. Customer Creation Issues

**Error:** "No such customer"
```ruby
# The service automatically handles this by recreating the customer
def ensure_stripe_customer
  if @user.stripe_customer_id.present?
    begin
      return Stripe::Customer.retrieve(@user.stripe_customer_id)
    rescue Stripe::InvalidRequestError
      # Customer doesn't exist, create new one
    end
  end
  # Creates new customer...
end
```

### Testing Issues

#### Test Cards Not Working
1. Verify you're using Stripe test API keys (`sk_test_...`)
2. Check that test cards are properly formatted
3. Use these verified test cards:
   - Success: `****************`
   - Declined: `****************`
   - 3D Secure: `****************`

#### Payment Status Stuck
1. Check payment intent status in Stripe Dashboard
2. Verify polling implementation with exponential backoff
3. Check for network connectivity issues

### Development Setup Issues

#### Missing Environment Variables
```bash
# Required environment variables
STRIPE_SECRET_KEY=sk_test_...
STRIPE_PUBLISHABLE_KEY=pk_test_...
JWT_SECRET=your_jwt_secret
```

#### Database Connection Issues
Ensure MongoDB is running and accessible:
```bash
# Check MongoDB connection
mongosh --eval "db.runCommand('ping')"
```

### Production Issues

#### Live API Keys
1. Replace test keys with live keys
2. Update Stripe Dashboard settings
3. Test with real cards (small amounts)
4. Monitor Stripe Dashboard for errors

#### Webhook Setup (Optional)
If you choose to implement webhooks later:
```bash
# Stripe CLI for local testing
stripe listen --forward-to localhost:3000/api/stripe/webhooks
```

### Getting Help

1. **Check Stripe Dashboard**: View payment intents and error details
2. **Enable Debug Logging**: Add detailed logging to track issues
3. **Use Stripe CLI**: Test webhooks and API calls locally
4. **Review Network Requests**: Check API request/response data
5. **Consult Documentation**: Refer to Stripe API documentation

### Debug Logging

Add this to your service for detailed debugging:

```ruby
def log_payment_attempt(payment_intent_id, action)
  Rails.logger.info("Payment Debug - Action: #{action}, Intent: #{payment_intent_id}")
  payment_intent = Stripe::PaymentIntent.retrieve(payment_intent_id)
  Rails.logger.info("Payment Debug - Status: #{payment_intent.status}")
  Rails.logger.info("Payment Debug - Amount: #{payment_intent.amount}")
end
``` 