# Stripe API Reference

## Base Configuration

### Environment Variables

```bash
# Development
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...

# Production
STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_SECRET_KEY=sk_live_...
```

### API Base URL

```
Development: https://your-dev-api.com/api
Production: https://your-api.com/api
```

## Authentication

All API requests require authentication via Bearer token:

```
Authorization: Bearer <your_jwt_token>
```

## Cart Checkout APIs

### 1. Create Cart Payment Intent

**Endpoint:** `POST /api/cart/checkout`

**Description:** Creates a Stripe PaymentIntent for cart items

**Request Body:**
```json
{
  "shipping_address": {
    "name": "<PERSON> Doe",
    "line1": "123 Main St",
    "line2": "Apt 4B",
    "city": "New York",
    "state": "NY",
    "postal_code": "10001",
    "country": "US",
    "phone": "+1234567890"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "client_secret": "pi_3MtwBwLkdIwHu7ix28a3tqPa_secret_xxx",
    "payment_intent_id": "pi_3MtwBwLkdIwHu7ix28a3tqPa",
    "order_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "amount": 29.99,
    "currency": "USD"
  }
}
```

**Error Response:**
```json
{
  "success": false,
  "error": "Cart is empty - add items before checkout"
}
```

### 2. Confirm Cart Payment

**Endpoint:** `POST /api/cart/confirm_payment`

**Description:** Confirms payment with payment method

**Request Body:**
```json
{
  "payment_intent_id": "pi_3MtwBwLkdIwHu7ix28a3tqPa",
  "payment_method_id": "pm_1234567890"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "succeeded",
    "client_secret": "pi_3MtwBwLkdIwHu7ix28a3tqPa_secret_xxx",
    "next_action": null,
    "payment_intent_id": "pi_3MtwBwLkdIwHu7ix28a3tqPa"
  }
}
```

**3D Secure Response:**
```json
{
  "success": true,
  "data": {
    "status": "requires_action",
    "client_secret": "pi_3MtwBwLkdIwHu7ix28a3tqPa_secret_xxx",
    "next_action": {
      "type": "use_stripe_sdk",
      "use_stripe_sdk": {
        "type": "three_d_secure_redirect",
        "stripe_js": "..."
      }
    },
    "payment_intent_id": "pi_3MtwBwLkdIwHu7ix28a3tqPa"
  }
}
```

### 3. Check Payment Status

**Endpoint:** `GET /api/cart/payment_status`

**Query Parameters:**
- `payment_intent_id` (required): The PaymentIntent ID

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "succeeded",
    "order_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "amount_received": 2999,
    "charges": [
      {
        "id": "ch_3MtwBwLkdIwHu7ix28a3tqPa",
        "amount": 2999,
        "status": "succeeded",
        "created": 1680800504,
        "receipt_url": "https://pay.stripe.com/receipts/xxx"
      }
    ]
  }
}
```

### 4. Cancel Payment

**Endpoint:** `POST /api/cart/cancel_payment`

**Request Body:**
```json
{
  "payment_intent_id": "pi_3MtwBwLkdIwHu7ix28a3tqPa"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "cancelled"
  }
}
```

## Buy Now APIs

### 1. Create Buy Now Payment Intent

**Endpoint:** `POST /api/buy_now`

**Description:** Creates a PaymentIntent for single item purchase

**Request Body:**
```json
{
  "variant_id": "variant_123",
  "quantity": 2,
  "price": 15.99,
  "product_name": "Travel Backpack"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "client_secret": "pi_3MtwBwLkdIwHu7ix28a3tqPa_secret_xxx",
    "payment_intent_id": "pi_3MtwBwLkdIwHu7ix28a3tqPa",
    "order_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "amount": 31.98,
    "currency": "USD"
  }
}
```

### 2. Confirm Buy Now Payment

**Endpoint:** `POST /api/buy_now/confirm_payment`

**Request Body:**
```json
{
  "payment_intent_id": "pi_3MtwBwLkdIwHu7ix28a3tqPa",
  "payment_method_id": "pm_1234567890"
}
```

**Response:** Same as cart confirm payment

### 3. Check Buy Now Payment Status

**Endpoint:** `GET /api/buy_now/payment_status`

**Query Parameters:**
- `payment_intent_id` (required): The PaymentIntent ID

**Response:** Same as cart payment status

## Payment Status Values

| Status | Description |
|--------|-------------|
| `requires_payment_method` | Payment method required |
| `requires_confirmation` | Payment method attached, needs confirmation |
| `requires_action` | Additional action required (3D Secure) |
| `processing` | Payment is being processed |
| `requires_capture` | Payment authorized, needs capture |
| `succeeded` | Payment completed successfully |
| `canceled` | Payment was canceled |

## Error Codes

### HTTP Status Codes

| Code | Description |
|------|-------------|
| 200 | Success |
| 400 | Bad Request - Invalid parameters |
| 401 | Unauthorized - Invalid or missing auth token |
| 404 | Not Found - Resource doesn't exist |
| 422 | Unprocessable Entity - Validation errors |
| 500 | Internal Server Error |

### Application Error Codes

```json
{
  "success": false,
  "error": "Error message",
  "error_code": "CART_EMPTY",
  "details": {
    "field": "cart_items",
    "message": "Cart must contain at least one item"
  }
}
```

Common error codes:
- `CART_EMPTY`: Cart has no items
- `INVALID_VARIANT`: Variant ID not found
- `PAYMENT_FAILED`: Payment processing failed
- `INSUFFICIENT_FUNDS`: Card declined due to insufficient funds
- `CARD_DECLINED`: Card was declined
- `EXPIRED_CARD`: Card has expired
- `INVALID_CVC`: CVC verification failed

## Rate Limiting

API requests are rate limited:
- **Development**: 100 requests per minute
- **Production**: 1000 requests per minute

Rate limit headers:
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## Webhooks (Optional)

If you choose to implement webhooks later:

**Endpoint:** `POST /api/stripe/webhooks`

**Headers:**
```
Stripe-Signature: t=1640995200,v1=signature_hash
```

**Events:**
- `payment_intent.succeeded`
- `payment_intent.payment_failed`
- `payment_intent.canceled`

## SDK Integration

### Flutter Stripe SDK

```dart
// Initialize
Stripe.publishableKey = 'pk_test_...';

// Create payment method
final paymentMethod = await Stripe.instance.createPaymentMethod(
  params: PaymentMethodParams.card(
    paymentMethodData: PaymentMethodData(),
  ),
);

// Present payment sheet
await Stripe.instance.initPaymentSheet(
  paymentSheetParameters: SetupPaymentSheetParameters(
    paymentIntentClientSecret: clientSecret,
    merchantDisplayName: 'TravelGator',
  ),
);

await Stripe.instance.presentPaymentSheet();
```

### API Client Example

```dart
class ApiClient {
  static const baseUrl = 'https://your-api.com/api';
  static final dio = Dio();

  static Future<Map<String, dynamic>> createCartCheckout({
    Map<String, dynamic>? shippingAddress,
  }) async {
    final response = await dio.post(
      '$baseUrl/cart/checkout',
      data: {'shipping_address': shippingAddress},
      options: Options(
        headers: {'Authorization': 'Bearer $authToken'},
      ),
    );
    
    return response.data;
  }

  static Future<Map<String, dynamic>> checkPaymentStatus(
    String paymentIntentId,
  ) async {
    final response = await dio.get(
      '$baseUrl/cart/payment_status',
      queryParameters: {'payment_intent_id': paymentIntentId},
      options: Options(
        headers: {'Authorization': 'Bearer $authToken'},
      ),
    );
    
    return response.data;
  }
}
```

## Testing

### Test Mode

Use test API keys for development:
- Publishable: `pk_test_...`
- Secret: `sk_test_...`

### Test Cards

| Card Number | Result |
|-------------|--------|
| `****************` | Success |
| `****************` | Declined |
| `****************` | 3D Secure |

### Test Webhooks

Use Stripe CLI for local testing:
```bash
stripe listen --forward-to localhost:3000/api/stripe/webhooks
```

## Security

### Best Practices

1. **Never expose secret keys** in client code
2. **Validate payment amounts** on server
3. **Verify payment intent ownership** before processing
4. **Use HTTPS** for all API calls
5. **Implement proper authentication**
6. **Log security events**
7. **Monitor for suspicious activity**

### PCI Compliance

- Use Stripe's secure payment forms
- Never store card details
- Use tokenization for saved cards
- Implement proper access controls

## Monitoring

### Key Metrics

- Payment success rate
- Average payment time
- Error rates by type
- 3D Secure completion rate

### Logging

Log important events:
```json
{
  "event": "payment_created",
  "payment_intent_id": "pi_xxx",
  "user_id": "user_123",
  "amount": 29.99,
  "timestamp": "2024-01-24T10:30:00Z"
}
```

### Alerts

Set up alerts for:
- High error rates
- Payment failures
- API timeouts
- Security events
