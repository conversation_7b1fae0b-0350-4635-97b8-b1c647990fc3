fixx# Stripe Client Integration Documentation

## Overview

This document describes how the Flutter client interacts with the Rails backend for Stripe payment processing without webhooks. The system uses direct API calls and polling for payment status updates.

## Architecture Flow

```
Flutter App → Rails API → Stripe API → Rails API → Flutter App
     ↓                                                    ↑
Payment UI ←------ Polling for Status ←------ Payment Intent
```

## Setup & Configuration

### 1. Flutter Dependencies

Add to `pubspec.yaml`:

```yaml
dependencies:
  flutter_stripe: ^11.1.0
  dio: ^5.4.0
  provider: ^6.1.1
```

### 2. Initialize Stripe

```dart
// main.dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await StripeService.init();
  runApp(MyApp());
}
```

### 3. Environment Configuration

```dart
// config/stripe_config.dart
class StripeConfig {
  static const String publishableKey = String.fromEnvironment(
    'STRIPE_PUBLISHABLE_KEY',
    defaultValue: 'pk_test_...',
  );
  
  static const String baseUrl = String.fromEnvironment(
    'API_BASE_URL',
    defaultValue: 'https://your-api.com',
  );
}
```

## API Endpoints

### Cart Checkout Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/cart/checkout` | Create payment intent for cart |
| POST | `/api/cart/confirm_payment` | Confirm payment with payment method |
| GET | `/api/cart/payment_status` | Check payment status |
| POST | `/api/cart/cancel_payment` | Cancel payment intent |

### Buy Now Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/buy_now` | Create payment intent for single item |
| POST | `/api/buy_now/confirm_payment` | Confirm buy now payment |
| GET | `/api/buy_now/payment_status` | Check buy now payment status |

## Payment Flows

### 1. Cart Checkout Flow

```dart
// Step 1: Create Payment Intent
final response = await dio.post('/api/cart/checkout', data: {
  'shipping_address': {
    'name': 'John Doe',
    'line1': '123 Main St',
    'city': 'New York',
    'state': 'NY',
    'postal_code': '10001',
    'country': 'US',
  }
});

final paymentData = response.data['data'];
// Returns: {
//   'client_secret': 'pi_xxx_secret_xxx',
//   'payment_intent_id': 'pi_xxx',
//   'order_id': 'order_xxx',
//   'amount': 29.99,
//   'currency': 'USD'
// }

// Step 2: Present Payment Sheet
await Stripe.instance.initPaymentSheet(
  paymentSheetParameters: SetupPaymentSheetParameters(
    paymentIntentClientSecret: paymentData['client_secret'],
    merchantDisplayName: 'TravelGator',
    style: ThemeMode.system,
  ),
);

await Stripe.instance.presentPaymentSheet();

// Step 3: Check Payment Status
final statusResponse = await dio.get('/api/cart/payment_status', 
  queryParameters: {
    'payment_intent_id': paymentData['payment_intent_id'],
  }
);

final status = statusResponse.data['data']['status'];
// Possible values: 'succeeded', 'processing', 'requires_action', 'canceled', 'failed'
```

### 2. Buy Now Flow

```dart
// Step 1: Create Payment Intent
final response = await dio.post('/api/buy_now', data: {
  'variant_id': 'variant_123',
  'quantity': 2,
  'price': 15.99,
  'product_name': 'Travel Backpack',
});

// Step 2-3: Same as cart checkout flow
```

### 3. Custom Payment Form Flow

```dart
// For advanced use cases with custom UI
final paymentMethod = await Stripe.instance.createPaymentMethod(
  params: PaymentMethodParams.card(
    paymentMethodData: PaymentMethodData(
      billingDetails: BillingDetails(
        name: 'John Doe',
        email: '<EMAIL>',
      ),
    ),
  ),
);

final confirmResponse = await dio.post('/api/cart/confirm_payment', data: {
  'payment_intent_id': paymentIntentId,
  'payment_method_id': paymentMethod.id,
});

// Handle 3D Secure if needed
if (confirmResponse.data['data']['status'] == 'requires_action') {
  await Stripe.instance.handleNextAction(clientSecret);
}
```

## Request/Response Formats

### Create Payment Intent Request

```json
{
  "shipping_address": {
    "name": "John Doe",
    "line1": "123 Main St",
    "line2": "Apt 4B",
    "city": "New York",
    "state": "NY",
    "postal_code": "10001",
    "country": "US",
    "phone": "+1234567890"
  }
}
```

### Create Payment Intent Response

```json
{
  "success": true,
  "data": {
    "client_secret": "pi_3MtwBwLkdIwHu7ix28a3tqPa_secret_xxx",
    "payment_intent_id": "pi_3MtwBwLkdIwHu7ix28a3tqPa",
    "order_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "amount": 29.99,
    "currency": "USD"
  }
}
```

### Payment Status Response

```json
{
  "success": true,
  "data": {
    "status": "succeeded",
    "order_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "amount_received": 2999,
    "charges": [
      {
        "id": "ch_3MtwBwLkdIwHu7ix28a3tqPa",
        "amount": 2999,
        "status": "succeeded",
        "created": 1680800504,
        "receipt_url": "https://pay.stripe.com/receipts/xxx"
      }
    ]
  }
}
```

## Error Handling

### Common Error Responses

```json
{
  "success": false,
  "error": "Cart is empty - add items before checkout"
}
```

### Stripe Error Handling

```dart
try {
  await Stripe.instance.presentPaymentSheet();
} on StripeException catch (e) {
  switch (e.error.code) {
    case FailureCode.Canceled:
      // User canceled payment
      break;
    case FailureCode.Failed:
      // Payment failed
      showError('Payment failed: ${e.error.localizedMessage}');
      break;
    case FailureCode.InvalidRequest:
      // Invalid request
      showError('Invalid payment request');
      break;
  }
}
```

### Network Error Handling

```dart
try {
  final response = await dio.post('/api/cart/checkout');
} on DioException catch (e) {
  switch (e.type) {
    case DioExceptionType.connectionTimeout:
      showError('Connection timeout. Please try again.');
      break;
    case DioExceptionType.receiveTimeout:
      showError('Server response timeout.');
      break;
    case DioExceptionType.badResponse:
      final errorMessage = e.response?.data['error'] ?? 'Unknown error';
      showError(errorMessage);
      break;
  }
}
```

## Payment Status Polling

### Simple Polling Implementation

```dart
Future<PaymentResult> pollPaymentStatus(
  String paymentIntentId, {
  Duration timeout = const Duration(minutes: 5),
  Duration interval = const Duration(seconds: 2),
}) async {
  final stopwatch = Stopwatch()..start();
  
  while (stopwatch.elapsed < timeout) {
    try {
      final response = await dio.get('/api/cart/payment_status',
        queryParameters: {'payment_intent_id': paymentIntentId},
      );
      
      final status = response.data['data']['status'];
      
      if (status == 'succeeded') {
        return PaymentResult.success(response.data['data']);
      } else if (status == 'canceled' || status == 'failed') {
        return PaymentResult.failed(status);
      }
      
      // Continue polling for: processing, requires_action
      await Future.delayed(interval);
      
    } catch (e) {
      // Continue polling on error
      await Future.delayed(interval);
    }
  }
  
  throw TimeoutException('Payment status check timed out');
}
```

### Advanced Polling with Exponential Backoff

```dart
Future<PaymentResult> pollWithBackoff(String paymentIntentId) async {
  int attempt = 0;
  const maxAttempts = 10;
  
  while (attempt < maxAttempts) {
    try {
      final status = await checkPaymentStatus(paymentIntentId);
      
      if (status.isComplete) {
        return status;
      }
      
      // Exponential backoff: 1s, 2s, 4s, 8s, max 30s
      final delay = Duration(
        seconds: math.min(math.pow(2, attempt).toInt(), 30),
      );
      
      await Future.delayed(delay);
      attempt++;
      
    } catch (e) {
      attempt++;
      if (attempt >= maxAttempts) rethrow;
    }
  }
  
  throw Exception('Max polling attempts reached');
}
```

## State Management

### Payment State Provider

```dart
// providers/payment_provider.dart
class PaymentProvider extends ChangeNotifier {
  PaymentState _state = PaymentState.idle;
  String? _paymentIntentId;
  String? _orderId;
  String? _errorMessage;

  PaymentState get state => _state;
  String? get paymentIntentId => _paymentIntentId;
  String? get orderId => _orderId;
  String? get errorMessage => _errorMessage;

  Future<void> processCartCheckout() async {
    _setState(PaymentState.loading);

    try {
      final result = await StripeService.processCartCheckout();

      if (result.success) {
        _paymentIntentId = result.paymentIntentId;
        _orderId = result.orderId;
        _setState(PaymentState.success);
      } else {
        _errorMessage = 'Payment failed';
        _setState(PaymentState.error);
      }
    } catch (e) {
      _errorMessage = e.toString();
      _setState(PaymentState.error);
    }
  }

  void _setState(PaymentState newState) {
    _state = newState;
    notifyListeners();
  }

  void reset() {
    _state = PaymentState.idle;
    _paymentIntentId = null;
    _orderId = null;
    _errorMessage = null;
    notifyListeners();
  }
}

enum PaymentState { idle, loading, success, error }
```

### Using Provider in Widgets

```dart
// widgets/checkout_button.dart
class CheckoutButton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<PaymentProvider>(
      builder: (context, paymentProvider, child) {
        return ElevatedButton(
          onPressed: paymentProvider.state == PaymentState.loading
              ? null
              : () => paymentProvider.processCartCheckout(),
          child: _buildButtonChild(paymentProvider.state),
        );
      },
    );
  }

  Widget _buildButtonChild(PaymentState state) {
    switch (state) {
      case PaymentState.loading:
        return CircularProgressIndicator();
      case PaymentState.success:
        return Icon(Icons.check);
      case PaymentState.error:
        return Text('Retry');
      default:
        return Text('Checkout');
    }
  }
}
```
```
