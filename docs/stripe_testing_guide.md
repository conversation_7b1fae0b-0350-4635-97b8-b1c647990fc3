# Stripe Testing Guide

## Test Cards

### Basic Test Cards

| Card Number | Brand | Description |
|-------------|-------|-------------|
| `****************` | Visa | Succeeds with any CVC and future expiry |
| `****************` | Visa | Always declined |
| `****************` | Visa | Declined - insufficient funds |
| `****************` | Visa | Declined - lost card |
| `****************` | Visa | Declined - stolen card |

### 3D Secure Test Cards

| Card Number | Description |
|-------------|-------------|
| `****************` | 3D Secure authentication required |
| `****************` | 3D Secure authentication may be required |
| `****************` | 3D Secure authentication not supported |

### International Test Cards

| Card Number | Country | Description |
|-------------|---------|-------------|
| `****************` | US | Debit card |
| `****************` | Canada | Visa |
| `****************` | Brazil | Visa |
| `****************` | Netherlands | Visa |

## Testing Scenarios

### 1. Successful Payment Flow

```dart
void testSuccessfulPayment() async {
  // Use successful test card
  final testCard = '****************';
  
  // Create payment intent
  final paymentIntent = await StripeService.createCartPaymentIntent();
  
  // Simulate successful payment
  final result = await StripeService.confirmPayment(
    paymentIntent.paymentIntentId,
    testCard,
  );
  
  assert(result.success == true);
  assert(result.status == 'succeeded');
}
```

### 2. Declined Payment Flow

```dart
void testDeclinedPayment() async {
  // Use declined test card
  final testCard = '****************';
  
  try {
    final paymentIntent = await StripeService.createCartPaymentIntent();
    await StripeService.confirmPayment(
      paymentIntent.paymentIntentId,
      testCard,
    );
    
    // Should not reach here
    assert(false, 'Payment should have been declined');
  } catch (e) {
    assert(e is PaymentException);
    assert(e.toString().contains('declined'));
  }
}
```

### 3. 3D Secure Flow

```dart
void test3DSecureFlow() async {
  final testCard = '****************';
  
  final paymentIntent = await StripeService.createCartPaymentIntent();
  final result = await StripeService.confirmPayment(
    paymentIntent.paymentIntentId,
    testCard,
  );
  
  // Should require additional action
  assert(result.status == 'requires_action');
  
  // Handle 3D Secure
  await Stripe.instance.handleNextAction(paymentIntent.clientSecret);
  
  // Check final status
  final finalStatus = await StripeService.checkPaymentStatus(
    paymentIntent.paymentIntentId,
  );
  
  assert(finalStatus['status'] == 'succeeded');
}
```

### 4. Network Error Handling

```dart
void testNetworkError() async {
  // Mock network failure
  when(mockDio.post(any)).thenThrow(
    DioException(
      type: DioExceptionType.connectionTimeout,
      requestOptions: RequestOptions(path: ''),
    ),
  );
  
  try {
    await StripeService.createCartPaymentIntent();
    assert(false, 'Should have thrown network error');
  } catch (e) {
    assert(e.toString().contains('timeout'));
  }
}
```

### 5. User Cancellation

```dart
void testUserCancellation() async {
  try {
    // Simulate user canceling payment sheet
    when(mockStripe.presentPaymentSheet()).thenThrow(
      StripeException(
        error: LocalizedErrorMessage(
          code: FailureCode.Canceled,
          localizedMessage: 'User canceled',
        ),
      ),
    );
    
    await StripeService.processCartCheckout();
    assert(false, 'Should have thrown cancellation error');
  } on StripeException catch (e) {
    assert(e.error.code == FailureCode.Canceled);
  }
}
```

## Widget Testing

### Testing Checkout Button

```dart
void main() {
  group('CheckoutButton Widget Tests', () {
    testWidgets('shows loading state during payment', (tester) async {
      final mockProvider = MockPaymentProvider();
      when(mockProvider.state).thenReturn(PaymentState.loading);
      
      await tester.pumpWidget(
        ChangeNotifierProvider<PaymentProvider>.value(
          value: mockProvider,
          child: MaterialApp(
            home: Scaffold(body: CheckoutButton()),
          ),
        ),
      );
      
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Checkout'), findsNothing);
    });

    testWidgets('shows success state after payment', (tester) async {
      final mockProvider = MockPaymentProvider();
      when(mockProvider.state).thenReturn(PaymentState.success);
      
      await tester.pumpWidget(
        ChangeNotifierProvider<PaymentProvider>.value(
          value: mockProvider,
          child: MaterialApp(
            home: Scaffold(body: CheckoutButton()),
          ),
        ),
      );
      
      expect(find.byIcon(Icons.check), findsOneWidget);
    });

    testWidgets('calls payment provider on tap', (tester) async {
      final mockProvider = MockPaymentProvider();
      when(mockProvider.state).thenReturn(PaymentState.idle);
      
      await tester.pumpWidget(
        ChangeNotifierProvider<PaymentProvider>.value(
          value: mockProvider,
          child: MaterialApp(
            home: Scaffold(body: CheckoutButton()),
          ),
        ),
      );
      
      await tester.tap(find.text('Checkout'));
      
      verify(mockProvider.processCartCheckout()).called(1);
    });
  });
}
```

### Testing Buy Now Button

```dart
testWidgets('BuyNowButton processes payment correctly', (tester) async {
  await tester.pumpWidget(
    MaterialApp(
      home: Scaffold(
        body: BuyNowButton(
          variantId: 'test_variant',
          quantity: 2,
          price: 15.99,
          productName: 'Test Product',
        ),
      ),
    ),
  );
  
  // Tap buy now button
  await tester.tap(find.text('Buy Now'));
  await tester.pump();
  
  // Should show loading
  expect(find.byType(CircularProgressIndicator), findsOneWidget);
  
  // Wait for completion
  await tester.pumpAndSettle();
  
  // Should show success dialog
  expect(find.text('Purchase Successful!'), findsOneWidget);
});
```

## Integration Testing

### End-to-End Payment Flow

```dart
// integration_test/payment_e2e_test.dart
void main() {
  group('Payment E2E Tests', () {
    testWidgets('complete cart checkout flow', (tester) async {
      await tester.pumpWidget(MyApp());
      
      // Add items to cart
      await tester.tap(find.text('Add to Cart'));
      await tester.pumpAndSettle();
      
      // Navigate to cart
      await tester.tap(find.byIcon(Icons.shopping_cart));
      await tester.pumpAndSettle();
      
      // Start checkout
      await tester.tap(find.text('Checkout'));
      await tester.pumpAndSettle();
      
      // Payment sheet should appear
      expect(find.text('Payment'), findsOneWidget);
      
      // Enter test card details
      await tester.enterText(
        find.byKey(Key('card_number_field')),
        '****************',
      );
      
      await tester.enterText(
        find.byKey(Key('expiry_field')),
        '12/25',
      );
      
      await tester.enterText(
        find.byKey(Key('cvc_field')),
        '123',
      );
      
      // Complete payment
      await tester.tap(find.text('Pay'));
      await tester.pumpAndSettle(Duration(seconds: 5));
      
      // Verify success
      expect(find.text('Payment Successful!'), findsOneWidget);
    });
  });
}
```

## Mock Setup

### Mock Stripe Service

```dart
class MockStripeService extends Mock implements StripeService {}

void setupMocks() {
  final mockStripe = MockStripeService();
  
  // Mock successful payment
  when(mockStripe.processCartCheckout()).thenAnswer(
    (_) async => PaymentIntentResult(
      success: true,
      paymentIntentId: 'pi_test_123',
      orderId: 'order_test_123',
      status: 'succeeded',
    ),
  );
  
  // Mock payment failure
  when(mockStripe.processBuyNowCheckout(
    variantId: 'invalid_variant',
    quantity: any,
    price: any,
    productName: any,
  )).thenThrow(PaymentException('Invalid variant'));
}
```

### Mock API Responses

```dart
class MockApiService {
  static void setupSuccessfulResponses() {
    when(mockDio.post('/api/cart/checkout')).thenAnswer(
      (_) async => Response(
        data: {
          'success': true,
          'data': {
            'client_secret': 'pi_test_secret',
            'payment_intent_id': 'pi_test',
            'order_id': 'order_test',
            'amount': 29.99,
            'currency': 'USD',
          }
        },
        statusCode: 200,
        requestOptions: RequestOptions(path: ''),
      ),
    );
    
    when(mockDio.get('/api/cart/payment_status')).thenAnswer(
      (_) async => Response(
        data: {
          'success': true,
          'data': {
            'status': 'succeeded',
            'order_id': 'order_test',
            'amount_received': 2999,
          }
        },
        statusCode: 200,
        requestOptions: RequestOptions(path: ''),
      ),
    );
  }
}
```

## Performance Testing

### Load Testing

```dart
void performanceTest() async {
  final stopwatch = Stopwatch()..start();
  
  // Test multiple concurrent payments
  final futures = List.generate(10, (index) async {
    return await StripeService.createCartPaymentIntent();
  });
  
  await Future.wait(futures);
  
  stopwatch.stop();
  
  // Should complete within reasonable time
  assert(stopwatch.elapsedMilliseconds < 5000);
}
```

### Memory Testing

```dart
void memoryTest() async {
  final initialMemory = ProcessInfo.currentRss;
  
  // Perform multiple payment operations
  for (int i = 0; i < 100; i++) {
    final intent = await StripeService.createCartPaymentIntent();
    await StripeService.cancelPayment(intent.paymentIntentId);
  }
  
  // Force garbage collection
  await Future.delayed(Duration(seconds: 1));
  
  final finalMemory = ProcessInfo.currentRss;
  final memoryIncrease = finalMemory - initialMemory;
  
  // Memory increase should be reasonable
  assert(memoryIncrease < 50 * 1024 * 1024); // 50MB
}
```

## Debugging Tips

### Enable Debug Logging

```dart
void enableDebugMode() {
  StripeService.debugMode = true;
  
  // Add request/response logging
  dio.interceptors.add(LogInterceptor(
    requestBody: true,
    responseBody: true,
    logPrint: (object) => print('[API] $object'),
  ));
}
```

### Payment Intent Debugging

```dart
void debugPaymentIntent(String paymentIntentId) async {
  final status = await StripeService.checkPaymentStatus(paymentIntentId);
  
  print('Payment Intent Debug:');
  print('ID: $paymentIntentId');
  print('Status: ${status['status']}');
  print('Amount: ${status['amount_received']}');
  print('Charges: ${status['charges']}');
}
```

### Error Tracking

```dart
void trackPaymentError(Exception error, String context) {
  // Send to crash reporting service
  FirebaseCrashlytics.instance.recordError(
    error,
    null,
    information: [
      'Context: $context',
      'Timestamp: ${DateTime.now()}',
      'User: ${getCurrentUserId()}',
    ],
  );
}
```
