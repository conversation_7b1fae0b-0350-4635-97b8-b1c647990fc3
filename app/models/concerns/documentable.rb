module Documentable
  extend ActiveSupport::Concern

  included do
    include Mongoid::Document
    include Mongoid::Timestamps

    include MongoidEnumerable

    index({
      created_at: 1,
    }, {
      background: true
    })
  end

  def add_error(field, message)
    self.errors.add(field, message.to_s)
  end

  def with_error(field, message)
    self.errors.add(field, message.to_s)

    self
  end
end