class User
  include Documentable

  field :firebase_uid, type: String
  field :email, type: String
  field :name, type: String
  field :shopify_customer_id, type: String
  field :stripe_customer_id, type: String
  field :created_at, type: Time
  field :updated_at, type: Time

  index({ firebase_uid: 1 }, { unique: true })
  index({ email: 1 })

  has_many :carts, dependent: :destroy
  has_many :orders, dependent: :destroy

  validates :firebase_uid, presence: true, uniqueness: true
  validates :email, presence: true
end
