class Cart
  include Documentable

  field :user_id, type: BSON::ObjectId
  field :items, type: Array, default: []
  field :total_price, type: Float, default: 0.0
  field :currency, type: String, default: "USD"
  field :status, type: String, default: "active"

  belongs_to :user

  validates :user_id, presence: true

  def add_item(variant_id, product_id, quantity, price, title = nil)
    existing_item = items.find { |item| item["variant_id"] == variant_id }

    if existing_item
      existing_item["quantity"] += quantity
    else
      items << {
        "variant_id" => variant_id,
        "product_id" => product_id,
        "quantity" => quantity,
        "price" => price,
        "title" => title,
      }
    end

    calculate_total
  end

  def remove_item(variant_id)
    items.reject! { |item| item["variant_id"] == variant_id }
    calculate_total
  end

  def update_item_quantity(variant_id, quantity)
    item = items.find { |item| item["variant_id"] == variant_id }
    if item
      item["quantity"] = quantity
      calculate_total
    end
  end

  def calculate_total
    self.total_price = items.sum { |item| item["price"] * item["quantity"] }
  end

  def items_count
    items.sum { |item| item["quantity"] }
  end
end
