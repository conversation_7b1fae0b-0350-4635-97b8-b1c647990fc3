class Order
  include Documentable

  field :user_id, type: BSON::ObjectId
  field :shopify_order_id, type: String
  field :shopify_checkout_url, type: String
  field :stripe_payment_intent_id, type: String
  field :payment_status, type: String
  field :order_type, type: String
  field :total_price, type: Float
  field :currency, type: String
  field :status, type: String
  field :items, type: Array

  belongs_to :user

  validates :user_id, presence: true
  validates :shopify_order_id, presence: true, unless: :stripe_payment_intent_id?
  validates :stripe_payment_intent_id, presence: true, unless: :shopify_order_id?

  # Custom validation to ensure at least one payment method is present
  validate :payment_method_present

  private

  def payment_method_present
    if shopify_order_id.blank? && stripe_payment_intent_id.blank?
      errors.add(:base, "Either Shopify order ID or Stripe payment intent ID must be present")
    end
  end
end
