class StripePaymentService
  include ErrorLogging

  def initialize(user, buyer_ip = nil)
    @user = user
    @buyer_ip = buyer_ip

    # Use environment variable for Stripe secret key
    stripe_secret_key = ENV['STRIPE_SECRET_KEY'] || Rails.application.credentials.dig(:stripe, :secret_key)
    raise "Stripe secret key not configured" if stripe_secret_key.blank?

    # Set the global Stripe API key
    Stripe.api_key = stripe_secret_key
  end

  # Tạo PaymentIntent cho cart checkout
  def create_cart_payment_intent(cart_items, options = {})
    validate_cart_items(cart_items)
    
    total_amount = calculate_total_amount(cart_items)
    
    # Tạo hoặc lấy Stripe Customer
    customer = ensure_stripe_customer

    payment_intent_params = {
      amount: (total_amount * 100).to_i, # Convert to cents
      currency: 'usd',
      customer: customer.id,
      metadata: {
        user_id: @user.id.to_s,
        order_type: 'cart_checkout',
        cart_id: options[:cart_id],
        items_count: cart_items.length
      },
      description: "Cart checkout for #{@user.email}",
      receipt_email: @user.email,
      automatic_payment_methods: {
        enabled: true,
        allow_redirects: 'never'  # Disable redirect-based payment methods for mobile
      },
      setup_future_usage: 'off_session' # Save payment method for future use
    }

    # Add shipping if provided
    if options[:shipping_address]
      payment_intent_params[:shipping] = format_shipping_address(options[:shipping_address])
    end

    payment_intent = Stripe::PaymentIntent.create(payment_intent_params)
    
    # Create order record immediately
    order = create_order_record(payment_intent, cart_items, 'cart_checkout')
    
    {
      client_secret: payment_intent.client_secret,
      payment_intent_id: payment_intent.id,
      order_id: order.id.to_s,
      amount: total_amount,
      currency: 'USD'
    }
  rescue StandardError => e
    log_error("Failed to create cart payment intent", e)
    raise "Failed to create payment: #{e.message}"
  end

  # Tạo PaymentIntent cho buy now
  def create_buy_now_payment_intent(variant_id, quantity, price, product_name)
    validate_buy_now_params(variant_id, quantity, price)
    
    total_amount = price * quantity
    customer = ensure_stripe_customer

    payment_intent_params = {
      amount: (total_amount * 100).to_i,
      currency: 'usd',
      customer: customer.id,
      metadata: {
        user_id: @user.id.to_s,
        order_type: 'buy_now',
        variant_id: variant_id,
        quantity: quantity.to_s,
        product_name: product_name
      },
      description: "Buy now: #{product_name}",
      receipt_email: @user.email,
      automatic_payment_methods: {
        enabled: true,
        allow_redirects: 'never'  # Disable redirect-based payment methods for mobile
      }
    }

    payment_intent = Stripe::PaymentIntent.create(payment_intent_params)
    
    # Create order record
    items = [{
      variant_id: variant_id,
      quantity: quantity,
      price: price,
      title: product_name
    }]
    
    order = create_order_record(payment_intent, items, 'buy_now')
    
    {
      client_secret: payment_intent.client_secret,
      payment_intent_id: payment_intent.id,
      order_id: order.id.to_s,
      amount: total_amount,
      currency: 'USD'
    }
  rescue StandardError => e
    log_error("Failed to create buy now payment intent", e)
    raise "Failed to create payment: #{e.message}"
  end

  # Confirm payment sau khi user nhập thông tin card
  def confirm_payment(payment_intent_id, payment_method_id = nil, card_details = nil)
    payment_intent = Stripe::PaymentIntent.retrieve(payment_intent_id)
    
    # Verify payment intent belongs to current user
    unless payment_intent.metadata.user_id == @user.id.to_s
      raise "Unauthorized payment intent access"
    end

    confirm_params = {}
    
    # Handle payment method creation from card details if provided
    if card_details.present? && payment_method_id.blank?
      payment_method_id = create_payment_method_from_card_details(card_details)
    end
    
    confirm_params[:payment_method] = payment_method_id if payment_method_id

    confirmed_intent = Stripe::PaymentIntent.confirm(payment_intent_id, confirm_params)
    
    # Update order status based on payment intent status
    update_order_from_payment_intent(confirmed_intent)
    
    {
      status: confirmed_intent.status,
      client_secret: confirmed_intent.client_secret,
      next_action: confirmed_intent.next_action,
      payment_intent_id: confirmed_intent.id
    }
  rescue StandardError => e
    log_error("Failed to confirm payment", e)
    raise "Failed to confirm payment: #{e.message}"
  end

  # Check payment status (thay thế webhook)
  def check_payment_status(payment_intent_id)
    payment_intent = Stripe::PaymentIntent.retrieve(payment_intent_id)
    
    # Verify ownership
    unless payment_intent.metadata.user_id == @user.id.to_s
      raise "Unauthorized payment intent access"
    end

    # Update order status
    order = update_order_from_payment_intent(payment_intent)
    
    {
      status: payment_intent.status,
      order_id: order&.id&.to_s,
      amount_received: payment_intent.amount_received,
      charges: payment_intent.charges.data.map { |charge| format_charge(charge) }
    }
  rescue StandardError => e
    log_error("Failed to check payment status", e)
    raise "Failed to check payment status: #{e.message}"
  end

  # Cancel payment intent
  def cancel_payment(payment_intent_id)
    payment_intent = Stripe::PaymentIntent.retrieve(payment_intent_id)

    unless payment_intent.metadata.user_id == @user.id.to_s
      raise "Unauthorized payment intent access"
    end

    if payment_intent.status == 'requires_payment_method' || payment_intent.status == 'requires_confirmation'
      Stripe::PaymentIntent.cancel(payment_intent_id)

      # Update order status
      order = Order.find_by(stripe_payment_intent_id: payment_intent_id)
      order&.update!(status: 'cancelled')

      { status: 'cancelled' }
    else
      raise "Cannot cancel payment in status: #{payment_intent.status}"
    end
  rescue StandardError => e
    log_error("Failed to cancel payment", e)
    raise "Failed to cancel payment: #{e.message}"
  end

  private

  def ensure_stripe_customer
    if @user.stripe_customer_id.present?
      begin
        return Stripe::Customer.retrieve(@user.stripe_customer_id)
      rescue Stripe::InvalidRequestError
        # Customer doesn't exist, create new one
      end
    end

    customer = Stripe::Customer.create({
      email: @user.email,
      name: @user.name,
      metadata: {
        user_id: @user.id.to_s
      }
    })

    @user.update!(stripe_customer_id: customer.id)
    customer
  end

  def create_payment_method_from_card_details(card_details)
    payment_method = Stripe::PaymentMethod.create({
      type: 'card',
      card: {
        number: card_details['number'],
        exp_month: card_details['exp_month'],
        exp_year: card_details['exp_year'],
        cvc: card_details['cvc']
      },
      billing_details: {
        name: card_details['name']
      }
    })
    
    payment_method.id
  rescue StandardError => e
    log_error("Failed to create payment method from card details", e)
    raise "Failed to create payment method: #{e.message}"
  end

  def validate_cart_items(cart_items)
    raise "Cart items cannot be empty" if cart_items.blank?
    
    cart_items.each do |item|
      raise "Invalid cart item: missing variant_id" unless item['variant_id'].present?
      raise "Invalid cart item: missing price" unless item['price'].present?
      raise "Invalid cart item: missing quantity" unless item['quantity'].present?
    end
  end

  def validate_buy_now_params(variant_id, quantity, price)
    raise "Variant ID is required" if variant_id.blank?
    raise "Quantity must be greater than 0" if quantity.to_i <= 0
    raise "Price must be greater than 0" if price.to_f <= 0
  end

  def calculate_total_amount(cart_items)
    cart_items.sum { |item| item['price'].to_f * item['quantity'].to_i }
  end

  def create_order_record(payment_intent, items, order_type)
    Order.create!(
      user: @user,
      stripe_payment_intent_id: payment_intent.id,
      total_price: payment_intent.amount / 100.0,
      currency: payment_intent.currency.upcase,
      status: 'pending',
      payment_status: payment_intent.status,
      items: items,
      order_type: order_type
    )
  rescue StandardError => e
    log_error("Failed to create order record", e)
    # Don't fail the entire flow if order record creation fails
    nil
  end

  def update_order_from_payment_intent(payment_intent)
    order = Order.find_by(stripe_payment_intent_id: payment_intent.id)
    return nil unless order

    new_status = case payment_intent.status
                 when 'succeeded'
                   'completed'
                 when 'canceled'
                   'cancelled'
                 when 'requires_action'
                   'requires_action'
                 when 'processing'
                   'processing'
                 else
                   'pending'
                 end

    update_params = {
      status: new_status,
      payment_status: payment_intent.status
    }

    if payment_intent.amount_received && payment_intent.amount_received > 0
      update_params[:total_price] = payment_intent.amount_received / 100.0
    end

    order.update!(update_params)

    # Clear cart if payment completed successfully
    if new_status == 'completed' && payment_intent.metadata.cart_id
      cart = Cart.find_by(id: payment_intent.metadata.cart_id)
      if cart
        # Clear cart items and mark as checked out
        cart.update!(items: [], status: 'checked_out')
        Rails.logger.info "Cart #{cart.id} cleared after successful payment #{payment_intent.id}"
      end
    end

    order
  end

  def format_shipping_address(address)
    # Handle both string and symbol keys from params
    # ActionController::Parameters already has indifferent access
    addr = address.respond_to?(:with_indifferent_access) && !address.is_a?(ActionController::Parameters) ?
           address.with_indifferent_access : address

    {
      address: {
        line1: addr[:line1],
        line2: addr[:line2],
        city: addr[:city],
        state: addr[:state],
        postal_code: addr[:postal_code],
        country: addr[:country]
      },
      name: addr[:name],
      phone: addr[:phone]
    }
  end

  def format_charge(charge)
    {
      id: charge.id,
      amount: charge.amount,
      status: charge.status,
      created: charge.created,
      receipt_url: charge.receipt_url
    }
  end

  def log_error(context, error)
    Rails.logger.error("#{context}: #{error.message}")
    Rails.logger.error("Backtrace: #{error.backtrace.first(5).join("\n")}")
  end
end
