class AuthService
  def initialize(firebase_auth_service: FirebaseAuthService.new, shopify_service: ShopifyService.new)
    @firebase_auth_service = firebase_auth_service
    @shopify_service = shopify_service
  end

  def authenticate(token)
    # Fail fast on critical validations
    raise "Authorization token is missing" if token.blank?

    firebase_user = @firebase_auth_service.verify_token(token)
    user = find_or_create_user(firebase_user)
    jwt_token = generate_jwt_token(user)

    { token: jwt_token, user: user_data(user) }
  end

  private

  def find_or_create_user(firebase_user)
    user = User.where(firebase_uid: firebase_user["localId"]).first

    if user
      handle_existing_user(user)
    else
      create_new_user(firebase_user)
    end
  end

  def handle_existing_user(user)
    # User exists in our database but doesn't have a Shopify customer ID yet
    return user if user.shopify_customer_id.present?

    existing_shopify_customer = @shopify_service.find_customer_by_email(user.email)

    if existing_shopify_customer
      # Link to existing Shopify customer
      user.update(shopify_customer_id: existing_shopify_customer["id"])
      raise user.errors.full_messages.join(", ") if user.errors.present?
    else
      # Create new Shopify customer for existing user
      shopify_customer = @shopify_service.create_customer(
        email: user.email,
        name: user.name,
      )
      user.update(shopify_customer_id: shopify_customer["id"])
      raise user.errors.full_messages.join(", ") if user.errors.present?
    end

    user
  end

  def create_new_user(firebase_user)
    # Check if user already exists in Shopify before creating a new customer
    existing_shopify_customer = @shopify_service.find_customer_by_email(firebase_user["email"])

    if existing_shopify_customer
      # User exists in Shopify but not in our database - link to existing Shopify customer
      shopify_customer_id = existing_shopify_customer["id"]
    else
      # User doesn't exist in Shopify - create new Shopify customer
      shopify_customer = @shopify_service.create_customer(
        email: firebase_user["email"],
        name: firebase_user["displayName"],
      )
      shopify_customer_id = shopify_customer["id"]
    end

    user = User.create(
      firebase_uid: firebase_user["localId"],
      email: firebase_user["email"],
      name: firebase_user["displayName"],
      shopify_customer_id: shopify_customer_id,
    )

    raise user.errors.full_messages.join(", ") unless user.persisted?
    user
  end

  def generate_jwt_token(user)
    JWT.encode(
      { user_id: user.id.to_s, exp: 24.hours.from_now.to_i },
      ENV["JWT_SECRET"] || "development_jwt_secret"
    )
  end

  def user_data(user)
    {
      id: user.id.to_s,
      email: user.email,
      name: user.name,
      shopify_customer_id: user.shopify_customer_id,
      created_at: user.created_at,
      updated_at: user.updated_at,
    }
  end
end
