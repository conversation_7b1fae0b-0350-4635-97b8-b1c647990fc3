class BuyNowService
  include ErrorLogging

  def initialize(user, buyer_ip = nil)
    @user = user
    @buyer_ip = buyer_ip
    @shopify_service = ShopifyService.new(buyer_ip)

    # Ensure user is provided for buy now functionality
    raise "User is required for buy now checkout" if @user.nil?
  end

  def create_checkout(variant_id, quantity = 1)
    validate_buy_now_params(variant_id, quantity)

    # Create checkout using Cart API with user's Shopify customer ID and email
    # For authenticated users, use email in buyerIdentity for better checkout experience
    checkout = @shopify_service.create_checkout(
      [{
        "variant_id" => variant_id,
        "quantity" => quantity
      }],
      @user.shopify_customer_id,
      nil, # customer_access_token - not needed for authenticated users
      @user.email # pass user email for buyer identity
    )

    # Validate checkout was created successfully
    raise "Failed to create Shopify checkout" if checkout.nil?

    # Create order record for tracking
    order = create_order_record(checkout, variant_id, quantity)

    format_response(checkout, order, authenticated: true)
  rescue StandardError => e
    log_error("Buy-now checkout failed", e)
    raise "Failed to create buy-now checkout: #{e.message}"
  end



  private

  def validate_buy_now_params(variant_id, quantity)
    raise "Variant ID is required" if variant_id.blank?
    raise "Quantity must be greater than 0" if quantity.to_i <= 0
  end



  def create_order_record(checkout, variant_id, quantity)
    Order.create(
      user: @user,
      shopify_order_id: checkout["id"],
      shopify_checkout_url: checkout["webUrl"],
      total_price: checkout.dig("totalPrice", "amount")&.to_f || 0.0,
      currency: checkout.dig("totalPrice", "currencyCode") || "USD",
      status: "pending",
      items: [{
        "variant_id" => variant_id,
        "quantity" => quantity,
        "price" => 0.0 # Will be updated when order is completed
      }]
    )
  rescue StandardError => e
    log_error("Failed to create order record", e)
    # Don't fail the entire flow if order record creation fails
    nil
  end

  def format_response(checkout, order = nil, authenticated: false)
    response = {
      success: true,
      checkout_url: checkout["webUrl"],
      shopify_cart_id: checkout["id"],
      total_price: checkout.dig("totalPrice", "amount"),
      currency: checkout.dig("totalPrice", "currencyCode"),
      authenticated: authenticated
    }

    if order
      response.merge!(
        order_id: order.id.to_s,
        local_order_created: true
      )
    end

    response
  end

  def log_error(context, error)
    Rails.logger.error("#{context}: #{error.message}")
    Rails.logger.error("Backtrace: #{error.backtrace.first(5).join("\n")}")
  end
end
