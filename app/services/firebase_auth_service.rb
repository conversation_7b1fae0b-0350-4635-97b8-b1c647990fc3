require "net/http"
require "uri"
require "json"
require "jwt"
require "openssl"
require "googleauth"
require "google/apis/identitytoolkit_v3"

class FirebaseAuthService
  include ErrorLogging

  def initialize
    setup_firebase_client
  end

  def verify_token(token)
    validate_token_input(token)
    decoded_token = decode_and_verify_token(token)
    format_user_data(decoded_token)
  rescue StandardError => e
    log_error("Token verification failed", e)
    raise "Token verification failed: #{e.message}"
  end

  # Additional Firebase Admin operations using Google API service
  def get_user_by_uid(uid)
    request = Google::Apis::IdentitytoolkitV3::GetAccountInfoRequest.new(
      local_id: [uid],
    )

    response = @identity_service.get_account_info(request)
    return nil unless response.users&.any?

    user = response.users.first
    format_user_info(user)
  rescue Google::Apis::Error => e
    log_error("Failed to get user by UID", e)
    raise "Failed to get user: #{e.message}"
  end

  def get_user_by_email(email)
    request = Google::Apis::IdentitytoolkitV3::GetAccountInfoRequest.new(
      email: [email],
    )

    response = @identity_service.get_account_info(request)
    return nil unless response.users&.any?

    user = response.users.first
    format_user_info(user)
  rescue Google::Apis::Error => e
    log_error("Failed to get user by email", e)
    raise "Failed to get user: #{e.message}"
  end

  private

  def setup_firebase_client
    service_account_path = Rails.root.join("config", "travelgator-firebase-adminsdk-fbsvc-7952955d76.json")

    unless File.exist?(service_account_path)
      raise "Firebase service account file not found at: #{service_account_path}"
    end

    @service_account_data = JSON.parse(File.read(service_account_path))
    @project_id = @service_account_data["project_id"]
    @private_key = OpenSSL::PKey::RSA.new(@service_account_data["private_key"])
    @client_email = @service_account_data["client_email"]

    # Initialize Google Auth credentials
    @credentials = Google::Auth::ServiceAccountCredentials.make_creds(
      json_key_io: File.open(service_account_path),
      scope: [
        "https://www.googleapis.com/auth/firebase.database",
        "https://www.googleapis.com/auth/userinfo.email",
      ],
    )

    # Initialize Identity Toolkit service
    @identity_service = Google::Apis::IdentitytoolkitV3::IdentityToolkitService.new
    @identity_service.authorization = @credentials
  end

  def validate_token_input(token)
    raise "Token is missing" if token.blank?
  end

  def format_user_data(decoded_token)
    {
      "localId" => decoded_token["sub"] || decoded_token["user_id"],
      "email" => decoded_token["email"],
      "displayName" => decoded_token["name"] || decoded_token["email"]&.split("@")&.first,
    }
  end

  def decode_and_verify_token(token)
    decoded_token = decode_token(token)
    validate_token_claims(decoded_token)
    decoded_token
  end

  def decode_token(token)
    begin
      # Decode the token header to get the key ID
      header = JWT.decode(token, nil, false)[1]
      kid = header["kid"]
      raise "Missing key ID in token header" unless kid

      # Get Firebase public keys
      public_keys = fetch_firebase_public_keys
      public_key_cert = public_keys[kid]
      raise "Invalid key ID" unless public_key_cert

      # Extract public key from certificate
      cert = OpenSSL::X509::Certificate.new(public_key_cert)
      public_key = cert.public_key

      # Verify and decode the token
      decoded_token = JWT.decode(
        token,
        public_key,
        true,
        {
          algorithm: "RS256",
          iss: "https://securetoken.google.com/#{@project_id}",
          aud: @project_id,
          verify_iat: true,
          verify_exp: true,
        }
      ).first

      decoded_token
    rescue JWT::DecodeError => e
      raise "Invalid token format: #{e.message}"
    end
  end

  def validate_token_claims(decoded_token)
    validate_token_expiration(decoded_token)
    validate_token_issuer(decoded_token)
    validate_token_audience(decoded_token)
    validate_token_subject(decoded_token)
  end

  def validate_token_expiration(decoded_token)
    exp = decoded_token["exp"]
    raise "Token has expired" if exp.nil? || Time.at(exp) < Time.now
  end

  def validate_token_issuer(decoded_token)
    expected_iss = "https://securetoken.google.com/#{@project_id}"
    raise "Invalid token issuer" if decoded_token["iss"] != expected_iss
  end

  def validate_token_audience(decoded_token)
    raise "Invalid token audience" if decoded_token["aud"] != @project_id
  end

  def validate_token_subject(decoded_token)
    raise "Token subject (sub) is missing" if decoded_token["sub"].blank?
  end

  def fetch_firebase_public_keys
    jwks_uri = "https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>"
    uri = URI(jwks_uri)
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true
    http.read_timeout = 10
    http.open_timeout = 10

    request = Net::HTTP::Get.new(uri)
    response = http.request(request)
    raise "Failed to fetch verification keys" unless response.code == "200"

    keys = JSON.parse(response.body)
    raise "Invalid public keys format" unless keys.is_a?(Hash) && !keys.empty?

    keys
  rescue JSON::ParserError => e
    raise "Invalid public keys response: #{e.message}"
  rescue Timeout::Error => e
    raise "Timeout fetching verification keys: #{e.message}"
  rescue StandardError => e
    raise "Failed to fetch verification keys: #{e.message}"
  end

  private

  def format_user_info(user)
    {
      "localId" => user.local_id,
      "email" => user.email,
      "displayName" => user.display_name,
      "emailVerified" => user.email_verified || false,
      "disabled" => user.disabled || false,
      "createdAt" => user.created_at,
      "lastLoginAt" => user.last_login_at,
      "providerUserInfo" => user.provider_user_info&.map do |provider|
        {
          "providerId" => provider.provider_id,
          "federatedId" => provider.federated_id,
          "email" => provider.email,
          "displayName" => provider.display_name,
        }
      end || [],
    }
  end

  def log_error(context, error)
    Rails.logger.error("#{context}: #{error.message}")
    Rails.logger.error("Backtrace: #{error.backtrace.first(5).join("\n")}")
  end
end
