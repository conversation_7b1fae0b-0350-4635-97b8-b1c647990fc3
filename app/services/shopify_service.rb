class ShopifyService
  include ErrorLogging

  def initialize(buyer_ip = nil)
    @buyer_ip = buyer_ip
    validate_configuration
    setup_shopify_client
  end

  def create_customer(user_data)
    validate_user_data(user_data)

    # First, try to find existing customer by email
    existing_customer = find_customer_by_email(user_data[:email])
    return existing_customer if existing_customer

    # Create new customer
    customer_data = build_customer_data(user_data)
    response = make_request("POST", "/customers.json", customer_data)
    validate_customer_creation_response(response)
    response["customer"]
  rescue StandardError => e
    # If customer creation fails due to duplicate email, try to find the existing customer
    if e.message.include?("422") && e.message.include?("email")
      existing_customer = find_customer_by_email(user_data[:email])
      return existing_customer if existing_customer
    end
    log_error("Customer creation failed", e)
    raise "Failed to create customer: #{e.message}"
  end

  def find_customer_by_email(email)
    validate_email(email)
    encoded_email = URI.encode_www_form_component(email)
    response = make_request("GET", "/customers/search.json?query=email:#{encoded_email}")
    extract_customer_from_response(response)
  rescue StandardError => e
    log_error("Failed to find customer", e)
    raise "Failed to find customer: #{e.message}"
  end

  def create_checkout(cart_items, customer_id = nil, customer_access_token = nil, customer_email = nil)
    validate_cart_items(cart_items)

    # Use Cart API as primary method (2025 best practice)
    cart = create_storefront_cart(cart_items, customer_id, customer_access_token, customer_email)
    return cart if cart

    # Fallback to draft order for B2B scenarios
    draft_order = create_draft_order(cart_items, customer_id)
    return format_draft_order_response(draft_order) if draft_order

    raise "Failed to create checkout - both methods failed"
  end

  def create_customer_access_token(email, password)
    mutation = <<~GRAPHQL
      mutation customerAccessTokenCreate($input: CustomerAccessTokenCreateInput!) {
        customerAccessTokenCreate(input: $input) {
          customerAccessToken {
            accessToken
            expiresAt
          }
          customerUserErrors {
            field
            message
          }
        }
      }
    GRAPHQL

    variables = {
      input: {
        email: email,
        password: password
      }
    }

    response = make_storefront_request(mutation, variables)
    result = response.dig("data", "customerAccessTokenCreate")

    if result["customerUserErrors"].any?
      raise "Authentication failed: #{result["customerUserErrors"].first["message"]}"
    end

    result["customerAccessToken"]
  rescue StandardError => e
    log_error("Failed to create customer access token", e)
    raise "Failed to authenticate customer: #{e.message}"
  end

  # Buy-now specific method for direct checkout
  def create_buy_now_checkout(variant_id, quantity = 1, customer_access_token = nil, customer_email = nil)
    cart_items = [{
      "variant_id" => variant_id,
      "quantity" => quantity
    }]

    create_storefront_cart(cart_items, nil, customer_access_token, customer_email)
  rescue StandardError => e
    log_error("Failed to create buy-now checkout", e)
    raise "Failed to create buy-now checkout: #{e.message}"
  end

  private

  def validate_configuration
    shop_name = ENV["SHOPIFY_SHOP_NAME"]
    admin_access_token = ENV["SHOPIFY_ACCESS_TOKEN"]
    storefront_access_token = ENV["SHOPIFY_STOREFRONT_ACCESS_TOKEN"]

    raise "Missing Shopify configuration" if shop_name.blank? || admin_access_token.blank?
    raise "Missing Shopify Storefront access token" if storefront_access_token.blank?
  end

  def setup_shopify_client
    @base_url = "https://#{ENV["SHOPIFY_SHOP_NAME"]}.myshopify.com/admin/api/2025-04"
    @headers = {
      "X-Shopify-Access-Token" => ENV["SHOPIFY_ACCESS_TOKEN"],
      "Content-Type" => "application/json",
    }
    @storefront_headers = {
      "X-Shopify-Storefront-Access-Token" => ENV["SHOPIFY_STOREFRONT_ACCESS_TOKEN"],
      "Content-Type" => "application/json",
    }
  end

  def validate_user_data(user_data)
    raise "Missing user data" if user_data.blank?
    raise "Missing email" if user_data[:email].blank?
  end

  def validate_email(email)
    raise "Missing email" if email.blank?
  end

  def validate_cart_items(cart_items)
    raise "Missing cart items" if cart_items.blank?
    raise "Invalid cart items format" unless cart_items.is_a?(Array)
  end

  def validate_customer_creation_response(response)
    raise "Failed to create customer" unless response && response["customer"] && response["customer"]["id"].present?
  end

  def build_customer_data(user_data)
    {
      customer: {
        email: user_data[:email],
        first_name: user_data[:name]&.split(" ")&.first,
        last_name: user_data[:name]&.split(" ")&.last,
      },
    }
  end

  def extract_customer_from_response(response)
    return nil unless response && response["customers"] && response["customers"].any?
    response["customers"].first
  end

  def create_draft_order(cart_items, customer_id = nil)
    mutation = <<~GRAPHQL
      mutation draftOrderCreate($input: DraftOrderInput!) {
        draftOrderCreate(input: $input) {
          draftOrder {
            id
            invoiceUrl
            totalPrice
            currencyCode
          }
          userErrors {
            field
            message
          }
        }
      }
    GRAPHQL

    variables = build_draft_order_variables(cart_items, customer_id)
    response = make_admin_request(mutation, variables)
    return nil unless response && response["data"]&.dig("draftOrderCreate", "draftOrder")

    format_draft_order_data(response["data"]["draftOrderCreate"]["draftOrder"])
  rescue StandardError => e
    log_error("Failed to create draft order", e)
    raise "Failed to create draft order: #{e.message}"
  end

  def build_draft_order_variables(cart_items, customer_id)
    variables = {
      input: {
        lineItems: cart_items.map { |item|
          variant_id = item["variant_id"].to_s.gsub(/^gid:\/\/shopify\/ProductVariant\//, "")
          {
            variantId: "gid://shopify/ProductVariant/#{variant_id}",
            quantity: item["quantity"],
          }
        },
      },
    }

    variables[:input][:customerId] = "gid://shopify/Customer/#{customer_id}" if customer_id.present?
    variables
  end

  def format_draft_order_data(draft_order)
    {
      "id" => draft_order["id"],
      "invoiceUrl" => draft_order["invoiceUrl"],
      "totalPrice" => draft_order["totalPrice"],
      "currencyCode" => draft_order["currencyCode"],
    }
  end

  def format_draft_order_response(draft_order)
    {
      "id" => draft_order["id"],
      "webUrl" => draft_order["invoiceUrl"],
      "totalPrice" => {
        "amount" => draft_order["totalPrice"],
        "currencyCode" => draft_order["currencyCode"],
      },
      "isDraft" => true,
    }
  end

  def create_storefront_cart(cart_items, customer_id = nil, customer_access_token = nil, customer_email = nil)
    mutation = <<~GRAPHQL
      mutation cartCreate($input: CartInput!) {
        cartCreate(input: $input) {
          cart {
            id
            checkoutUrl
            buyerIdentity {
              email
            }
            cost {
              totalAmount {
                amount
                currencyCode
              }
            }
          }
          userErrors {
            field
            message
          }
        }
      }
    GRAPHQL

    variables = build_cart_variables(cart_items, customer_id, customer_access_token, customer_email)
    response = make_storefront_request(mutation, variables)

    # Check for GraphQL errors
    if response.dig("data", "cartCreate", "userErrors")&.any?
      errors = response["data"]["cartCreate"]["userErrors"]
      raise "Shopify cart creation failed: #{errors.map { |e| e["message"] }.join(", ")}"
    end

    # Check if cart was created successfully
    unless response && response["data"]&.dig("cartCreate", "cart")
      Rails.logger.error("Cart creation failed - Response: #{response.inspect}")
      raise "Failed to create cart - no cart data returned from Shopify"
    end

    cart_data = response["data"]["cartCreate"]["cart"]

    format_cart_data(cart_data)
  rescue StandardError => e
    log_error("Failed to create cart", e)
    raise "Failed to create cart: #{e.message}"
  end

  def update_cart_buyer_identity(cart_id, customer_access_token = nil, customer_email = nil)
    mutation = <<~GRAPHQL
      mutation cartBuyerIdentityUpdate($cartId: ID!, $buyerIdentity: CartBuyerIdentityInput!) {
        cartBuyerIdentityUpdate(cartId: $cartId, buyerIdentity: $buyerIdentity) {
          cart {
            id
            checkoutUrl
            buyerIdentity {
              email
            }
            cost {
              totalAmount {
                amount
                currencyCode
              }
            }
          }
          userErrors {
            field
            message
          }
        }
      }
    GRAPHQL

    buyer_identity = {}

    # Add customer access token if available
    if customer_access_token.present?
      buyer_identity[:customerAccessToken] = customer_access_token
    end

    # Add email if available
    if customer_email.present?
      buyer_identity[:email] = customer_email
    end

    variables = {
      cartId: cart_id,
      buyerIdentity: buyer_identity
    }

    response = make_storefront_request(mutation, variables)

    if response.dig("data", "cartBuyerIdentityUpdate", "userErrors")&.any?
      errors = response["data"]["cartBuyerIdentityUpdate"]["userErrors"]
      raise "Failed to update buyer identity: #{errors.first["message"]}"
    end

    response.dig("data", "cartBuyerIdentityUpdate", "cart")
  rescue StandardError => e
    log_error("Failed to update cart buyer identity", e)
    raise "Failed to update cart buyer identity: #{e.message}"
  end

  def build_cart_variables(cart_items, customer_id = nil, customer_access_token = nil, customer_email = nil)
    variables = {
      input: {
        lines: cart_items.map { |item|
          variant_id = item["variant_id"].to_s.gsub(/^gid:\/\/shopify\/ProductVariant\//, "")
          {
            merchandiseId: "gid://shopify/ProductVariant/#{variant_id}",
            quantity: item["quantity"],
          }
        },
      },
    }

    # Add buyer identity for authenticated checkouts
    buyer_identity = {}

    # Prefer customer access token if available
    if customer_access_token.present?
      buyer_identity[:customerAccessToken] = customer_access_token
    end

    # Add email for authenticated users (helps with checkout flow)
    if customer_email.present?
      buyer_identity[:email] = customer_email
    end

    # Only add buyerIdentity if we have something to add
    if buyer_identity.any?
      variables[:input][:buyerIdentity] = buyer_identity
    end

    variables
  end

  def format_cart_data(cart_data)
    {
      "id" => cart_data["id"],
      "webUrl" => cart_data["checkoutUrl"],
      "totalPrice" => cart_data["cost"]["totalAmount"],
    }
  end

  def make_request(method, endpoint, data = nil)
    uri = URI("#{@base_url}#{endpoint}")
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true

    request = case method
      when "GET" then Net::HTTP::Get.new(uri)
      when "POST" then Net::HTTP::Post.new(uri)
      when "PUT" then Net::HTTP::Put.new(uri)
      when "DELETE" then Net::HTTP::Delete.new(uri)
      end

    @headers.each { |key, value| request[key] = value }
    request.body = data.to_json if data

    response = http.request(request)
    raise "API error: #{response.code} - #{response.body}" unless response.code.start_with?("2")

    JSON.parse(response.body)
  rescue JSON::ParserError => e
    raise "Invalid JSON response: #{e.message}"
  rescue StandardError => e
    raise "Request failed: #{e.message}"
  end

  def make_admin_request(query, variables)
    admin_url = "https://#{ENV["SHOPIFY_SHOP_NAME"]}.myshopify.com/admin/api/2025-04/graphql.json"
    make_graphql_request(admin_url, query, variables, @headers)
  end

  def make_storefront_request(query, variables)
    storefront_url = "https://#{ENV["SHOPIFY_SHOP_NAME"]}.myshopify.com/api/2025-04/graphql.json"

    # Add buyer IP header for server-side requests (required for authenticated checkouts)
    headers = @storefront_headers.dup
    headers["Shopify-Storefront-Buyer-IP"] = get_buyer_ip if get_buyer_ip

    make_graphql_request(storefront_url, query, variables, headers)
  end

  def make_graphql_request(url, query, variables, headers)
    uri = URI(url)
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true

    request = Net::HTTP::Post.new(uri)
    headers.each { |key, value| request[key] = value }
    request.body = { query: query, variables: variables }.to_json

    response = http.request(request)
    raise "GraphQL API error: #{response.code} - #{response.body}" unless response.code.start_with?("2")

    JSON.parse(response.body)
  rescue JSON::ParserError => e
    raise "Invalid GraphQL response: #{e.message}"
  rescue StandardError => e
    raise "GraphQL request failed: #{e.message}"
  end

  def get_buyer_ip
    @buyer_ip
  end

  def log_error(context, error)
    Rails.logger.error("#{context}: #{error.message}")
    Rails.logger.error("Backtrace: #{error.backtrace.first(5).join("\n")}")
  end
end
