module Api
  class BaseController < ApplicationController
    # Note: CSRF protection not needed for API controllers
    # API authentication is handled via tokens in headers

    protected

    def invalid_action!(message)
      render json: {
        error: message,
      }, status: 422
    end

    def invalid_resource!(resource)
      render json: {
        error: resource.errors.full_messages.to_sentence,
      }, status: 422
    end
  end
end
