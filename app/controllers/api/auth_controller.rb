module Api
  class AuthController < BaseController
    def authenticate
      token = request.headers["Authorization"]&.gsub("Bearer ", "")
      result = auth_service.authenticate(token)
      render_success(result)
    rescue StandardError => e
      render_error(e.message)
    end

    def verify
      token = extract_token_from_header

      if token.blank?
        render json: {
          success: false,
          message: "No token provided",
          user: nil
        }, status: :unauthorized
        return
      end

      begin
        # Verify and decode JWT token
        decoded_token = JWT.decode(token, ENV["JWT_SECRET"] || "development_jwt_secret", true, { algorithm: 'HS256' })
        user_data = decoded_token[0]

        # Fetch user data from database
        user = User.find(user_data['user_id'])

        if user
          render json: {
            success: true,
            message: "Token verified successfully",
            user: {
              id: user.id.to_s,
              email: user.email,
              name: user.name,
              firebase_uid: user.firebase_uid,
              created_at: user.created_at,
              updated_at: user.updated_at
            }
          }, status: :ok
        else
          render json: {
            success: false,
            message: "User not found",
            user: nil
          }, status: :not_found
        end

      rescue JWT::DecodeError => e
        render json: {
          success: false,
          message: "Invalid token: #{e.message}",
          user: nil
        }, status: :unauthorized
      rescue JWT::ExpiredSignature
        render json: {
          success: false,
          message: "Token has expired",
          user: nil
        }, status: :unauthorized
      rescue Mongoid::Errors::DocumentNotFound
        render json: {
          success: false,
          message: "User not found",
          user: nil
        }, status: :not_found
      rescue => e
        render json: {
          success: false,
          message: "Token verification failed: #{e.message}",
          user: nil
        }, status: :internal_server_error
      end
    end

    private

    def auth_service
      @auth_service ||= AuthService.new
    end

    def render_success(result)
      render json: { token: result[:token], user: result[:user] }
    end

    def render_error(message)
      render json: { error: message }, status: :unprocessable_entity
    end

    def extract_token_from_header
      auth_header = request.headers['Authorization']
      return nil unless auth_header&.start_with?('Bearer ')

      auth_header.split(' ').last
    end
  end
end
