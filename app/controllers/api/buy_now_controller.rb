module Api
  class BuyNowController < BaseController
    include Authenticatable

    def create_checkout
      stripe_service = StripePaymentService.new(@current_user, request.remote_ip)

      result = stripe_service.create_buy_now_payment_intent(
        params[:variant_id],
        params[:quantity]&.to_i || 1,
        params[:price].to_f,
        params[:product_name]
      )

      render_success(result)
    rescue StandardError => e
      render_error(e.message)
    end

    # Confirm buy now payment with payment method created by Stripe Payment Sheet
    def confirm_payment
      stripe_service = StripePaymentService.new(@current_user, request.remote_ip)
      result = stripe_service.confirm_payment(
        params[:payment_intent_id],
        params[:payment_method_id]
      )

      render_success(result)
    rescue StandardError => e
      render_error(e.message)
    end

    # Check buy now payment status
    def payment_status
      stripe_service = StripePaymentService.new(@current_user, request.remote_ip)
      result = stripe_service.check_payment_status(params[:payment_intent_id])

      render_success(result)
    rescue StandardError => e
      render_error(e.message)
    end

    private

    def buy_now_params
      params.permit(:variant_id, :quantity)
    end
  end
end
