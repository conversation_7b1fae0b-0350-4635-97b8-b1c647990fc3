module Api
  class OrdersController < BaseController
    include Authenticatable

    def index
      orders = @current_user.orders.order(created_at: :desc)
      render_success(orders.map { |order| order_data(order) })
    end

    def show
      order = @current_user.orders.find(params[:id])
      render_success(order_data(order))
    rescue Mongoid::Errors::DocumentNotFound
      render_error("Order not found")
    end

    private

    def order_data(order)
      {
        id: order.id.to_s,
        shopify_order_id: order.shopify_order_id,
        shopify_checkout_url: order.shopify_checkout_url,
        total_price: order.total_price,
        currency: order.currency,
        status: order.status,
        items: order.items,
        created_at: order.created_at,
        updated_at: order.updated_at,
      }
    end
  end
end
