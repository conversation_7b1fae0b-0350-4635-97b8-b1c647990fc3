module Api
  class CartsController < BaseController
    include Authenticatable
    before_action :cart_service

    def show
      cart = @cart_service.find_or_create_cart
      render_success(cart_data(cart))
    end

    def add_item
      cart = @cart_service.add_item(params)
      render_success(cart_data(cart))
    rescue StandardError => e
      render_error(e.message)
    end

    def remove_item
      cart = @cart_service.remove_item(params[:variant_id])
      render_success(cart_data(cart))
    rescue StandardError => e
      render_error(e.message)
    end

    def update_item
      cart = @cart_service.update_item_quantity(params[:variant_id], params[:quantity])
      render_success(cart_data(cart))
    rescue StandardError => e
      render_error(e.message)
    end

    def checkout
      cart = @cart_service.find_or_create_cart

      # Log cart state for debugging
      Rails.logger.info "Cart checkout attempt - Cart ID: #{cart.id}, Items count: #{cart.items.length}, Items: #{cart.items}"

      if cart.items.blank?
        Rails.logger.warn "Cart checkout failed - cart is empty for user #{@current_user.id}"
        raise "Cart is empty - add items before checkout"
      end

      stripe_service = StripePaymentService.new(@current_user, request.remote_ip)
      result = stripe_service.create_cart_payment_intent(
        cart.items,
        cart_id: cart.id.to_s,
        shipping_address: params[:shipping_address]
      )

      render_success(result)
    rescue StandardError => e
      Rails.logger.error "Cart checkout error: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      render_error(e.message)
    end

    # Confirm payment with payment method created by Stripe Payment Sheet
    def confirm_payment
      stripe_service = StripePaymentService.new(@current_user, request.remote_ip)
      result = stripe_service.confirm_payment(
        params[:payment_intent_id],
        params[:payment_method_id]
      )

      render_success(result)
    rescue StandardError => e
      render_error(e.message)
    end

    # Check payment status (polling endpoint)
    def payment_status
      stripe_service = StripePaymentService.new(@current_user, request.remote_ip)
      result = stripe_service.check_payment_status(params[:payment_intent_id])

      render_success(result)
    rescue StandardError => e
      render_error(e.message)
    end

    # Clear all items from cart
    def clear
      @cart_service.clear_cart
      render_success({ message: "Cart cleared successfully" })
    rescue StandardError => e
      render_error(e.message)
    end

    # Cancel payment
    def cancel_payment
      stripe_service = StripePaymentService.new(@current_user, request.remote_ip)
      result = stripe_service.cancel_payment(params[:payment_intent_id])

      render_success(result)
    rescue StandardError => e
      render_error(e.message)
    end

    private

    def cart_service
      @cart_service = CartService.new(@current_user, request.remote_ip)
    end

    def cart_data(cart)
      {
        id: cart.id.to_s,
        items: cart.items,
        total_price: cart.total_price,
        currency: cart.currency,
        items_count: cart.items_count,
      }
    end
  end
end
